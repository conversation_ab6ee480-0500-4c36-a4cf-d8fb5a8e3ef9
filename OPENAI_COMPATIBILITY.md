# OpenAI API 兼容性指南

## 常见问题

### ChatFinishReason 兼容性问题

如果您遇到 `Unknown ChatFinishReason value: end` 错误，这通常是由于使用了非 OpenAI 官方的 API 端点导致的。

#### 问题原因
- 非 OpenAI 官方的 API 服务可能返回 `"end"` 而不是标准的 `"stop"`
- Microsoft.SemanticKernel 对 ChatFinishReason 值进行严格校验

#### 解决方案

##### 1. 自动修复机制（已实现）✅
项目已内置 `ChatFinishReasonFixHandler`，自动将非标准值转换为标准值：
- `"end"` → `"stop"`
- `"finished"` → `"stop"`
- `"complete"` → `"stop"`
- `"done"` → `"stop"`
- `"max_tokens"` → `"length"`
- `"token_limit"` → `"length"`

该处理器已集成到 `KernelFactory` 中，无需额外配置。

##### 2. 使用官方 OpenAI API
确保在 `appsettings.json` 中配置官方 OpenAI API：
```json
{
  "Endpoint": "https://api.openai.com/v1",
  "ChatApiKey": "your-openai-api-key",
  "ChatModel": "gpt-4",
  "AnalysisModel": "gpt-4"
}
```

##### 3. Azure OpenAI 配置
如果使用 Azure OpenAI，请确保端点格式正确：
```json
{
  "Endpoint": "https://your-resource.openai.azure.com/",
  "ChatApiKey": "your-azure-api-key",
  "ChatModel": "gpt-4",
  "ModelProvider": "AzureOpenAI"
}
```

## 支持的 API 端点

### 官方支持
- ✅ OpenAI API (`https://api.openai.com/v1`)
- ✅ Azure OpenAI (`https://your-resource.openai.azure.com/`)

### 第三方兼容 API
- ⚠️ 其他兼容 OpenAI 的 API 服务可能存在兼容性问题
- 建议升级到最新版本的 Semantic Kernel 以获得更好的兼容性

## 故障排除

### 启用详细日志
在 `appsettings.json` 中启用详细 API 日志：
```json
{
  "EnableDetailedApiLogging": true
}
```

### 检查 API 响应
项目已内置异常处理机制，会自动重试兼容性问题。查看控制台输出获取详细错误信息。

## 更新指南

定期更新依赖项以获得最新的兼容性修复：
```bash
dotnet restore
dotnet build
```
