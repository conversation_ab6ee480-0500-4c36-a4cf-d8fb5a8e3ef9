@echo off
REM KoalaWiki Dotenv Startup Script for Windows
REM This script starts the backend and frontend with environment variable configuration

echo 🚀 Starting KoalaWiki with dotenv configuration...

REM Check if .env files exist
echo [INFO] Checking environment configuration...

REM Backend .env file
set BACKEND_ENV_FILE=src\KoalaWiki\.env
if not exist "%BACKEND_ENV_FILE%" (
    if exist "src\KoalaWiki\.env copy" (
        echo [WARNING] Backend .env not found, copying from .env copy
        copy "src\KoalaWiki\.env copy" "%BACKEND_ENV_FILE%"
        echo [INFO] Created backend .env file
    ) else (
        echo [ERROR] Backend .env file not found. Please create src\KoalaWiki\.env
        pause
        exit /b 1
    )
) else (
    echo [INFO] Backend .env file found
)

REM Frontend .env.local file
set FRONTEND_ENV_FILE=web\.env.local
if not exist "%FRONTEND_ENV_FILE%" (
    echo [WARNING] Frontend .env.local not found, creating default
    echo NEXT_PUBLIC_API_URL=http://localhost:5085 > "%FRONTEND_ENV_FILE%"
    echo [INFO] Created frontend .env.local file with default settings
) else (
    echo [INFO] Frontend .env.local file found
)

REM Start backend
echo [INFO] Starting backend service...
cd src\KoalaWiki

REM Check if the backend is built
if not exist "bin\Debug\net9.0\KoalaWiki.dll" (
    echo [INFO] Building backend...
    dotnet build
    if %errorlevel% neq 0 (
        echo [ERROR] Backend build failed
        pause
        exit /b 1
    )
)

REM Start backend with dotenv support
echo [INFO] Backend starting on http://localhost:5085
start "Backend" dotnet run --no-build

REM Wait a moment for backend to start
timeout /t 5 /nobreak >nul

REM Go back to root directory
cd ..\..

REM Start frontend
echo [INFO] Starting frontend service...
cd web

REM Check if node_modules exists
if not exist "node_modules" (
    echo [INFO] Installing frontend dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Frontend dependency installation failed
        pause
        exit /b 1
    )
)

REM Start frontend
echo [INFO] Frontend starting on http://localhost:3000
start "Frontend" npm run dev

echo.
echo [INFO] 🎉 KoalaWiki services started successfully!
echo Backend:  http://localhost:5085
echo Frontend: http://localhost:3000
echo.
echo [INFO] Environment variables loaded from:
echo   - Backend:  %BACKEND_ENV_FILE%
echo   - Frontend: %FRONTEND_ENV_FILE%
echo.
echo [WARNING] Close the terminal windows to stop the services
pause

