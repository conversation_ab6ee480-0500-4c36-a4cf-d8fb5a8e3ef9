apiVersion: app.sealos.io/v1
kind: Template
metadata:
  name: OpenDeepWiki
spec:
  title: 'OpenDeepWiki'
  url: 'https://opendeep.wiki'                             #Your app's official website
  gitRepo: 'https://github.com/AIDotNet/OpenDeepWiki'                          #Your app's github url
  author: 'ifzzh'                         #Your name
  description: 'OpenDeepWiki 是参考DeepWiki 作为灵感，基于 .NET 9 和 Semantic Kernel 开发的开源项目。它旨在帮助开发者更好地理解和使用代码库，提供代码分析、文档生成、知识图谱等功能。'                     #Descrip your app
  readme:  'https://raw.githubusercontent.com/AIDotNet/OpenDeepWiki/refs/heads/main/README.zh-CN.md'                         #Your app's readme.md url
  icon: 'https://opendeep.wiki/logo.png'                             #Your app's icon url
  templateType: inline
  defaults:
    web_port_name:
      type: string
      value: ${{ random(12) }}
    wiki_port_name:
      type: string
      value: ${{ random(12) }}
    app_host:
      type: string
      value: ${{ random(12) }}
    app_name:
      type: string
      value: opendeepwiki-${{ random(8) }}
    web_host:
      type: string
      value: ${{ random(12) }}
    web_name:
      type: string
      value: opendeepwiki-web-${{ random(8) }}
    model:
      type: string
      value: DeepSeek-V3
    api_server:
      type: string
      value: https://api.token-ai.cn/v1
  inputs:
    chat_model:
      description: '必须要支持function的模型'
      type: string
      default: ${{ defaults.model }}
      required: true
    analysis_model:
      description: '分析模型，用于生成仓库目录结构，建议gpt-4.1'
      type: string
      default: ${{ defaults.model }}
      required: true
    api_endpoint:
      description: '输入你的API接口'
      type: string
      default: ${{ defaults.api_server }}
      required: true
    api_key:
      description: '输入API Key'
      type: string
      default: ''
      required: true
    LANGUAGE:
      description: '中文 或 English'
      type: string
      default: '中文'
    KOALAWIKI_REPOSITORIES:
      description: '存放仓库的位置'
      type: string
      default: '/repositories'
    TASK_MAX_SIZE_PER_USER:
      description: '每个用户AI处理文档生成的最大数量'
      type: string
      default: '5'
    REPAIR_MERMAID:
      description: '是否修复Mermaid，1为修复，其他为不修复'
      type: string
      default: '1'
    DB_TYPE:
      description: '数据库类型'
      type: string
      default: 'sqlite'
    DB_CONNECTION_STRING:
      description: ''
      type: string
      default: 'Data Source=/data/KoalaWiki.db'
    UPDATE_INTERVAL:
      description: '仓库增量更新间隔'
      type: string
      default: '5'
    EnableSmartFilter:
      description: '是否启用智能过滤，这可能影响AI得到仓库的文件目录'
      type: string
      default: 'true'
    ENABLE_INCREMENTAL_UPDATE:
      description: '是否启用增量更新'
      type: string
      default: 'true'
    volume_size_repositories:
      description: '保存仓库 (Gi)'
      type: string
      default: '3'
      required: true
    volume_size_data:
      description: '保存文档（Gi）'
      type: string
      default: '1'
      required: true
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: ${{ defaults.app_name }}
  annotations:
    originImageName: crpi-j9ha7sxwhatgtvj4.cn-shenzhen.personal.cr.aliyuncs.com/koala-ai/koala-wiki                      #Your app's Docker image url
    deploy.cloud.sealos.io/minReplicas: '1'
    deploy.cloud.sealos.io/maxReplicas: '1'
  labels:
    cloud.sealos.io/app-deploy-manager: ${{ defaults.app_name }}
    app: ${{ defaults.app_name }}
spec:
  replicas: 1
  revisionHistoryLimit: 1
  minReadySeconds: 10
  serviceName: ${{ defaults.app_name }}
  selector:
    matchLabels:
      app: ${{ defaults.app_name }}
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 50%
  template:
    metadata:
      labels:
        app: ${{ defaults.app_name }}
    spec:
      automountServiceAccountToken: false
      terminationGracePeriodSeconds: 10
      containers:
        - name: ${{ defaults.app_name }}
          image: crpi-j9ha7sxwhatgtvj4.cn-shenzhen.personal.cr.aliyuncs.com/koala-ai/koala-wiki                                #Your app's Docker image url
          env: 
            - name: KOALAWIKI_REPOSITORIES
              value: ${{ inputs.KOALAWIKI_REPOSITORIES }}
            - name: TASK_MAX_SIZE_PER_USER
              value: ${{ inputs.TASK_MAX_SIZE_PER_USER }}
            - name: REPAIR_MERMAID
              value: ${{ inputs.REPAIR_MERMAID }}
            - name: CHAT_MODEL
              value: ${{ inputs.chat_model }}
            - name: ANALYSIS_MODEL
              value: ${{ inputs.analysis_model }}
            - name: CHAT_API_KEY
              value: ${{ inputs.api_key }}
            - name: ENDPOINT
              value: ${{ inputs.api_endpoint }}
            - name: DB_TYPE
              value: ${{ inputs.DB_TYPE }}
            - name: DB_CONNECTION_STRING
              value: ${{ inputs.DB_CONNECTION_STRING }}
            - name: UPDATE_INTERVAL
              value: ${{ inputs.UPDATE_INTERVAL }}
            - name: EnableSmartFilter
              value: ${{ inputs.EnableSmartFilter }}
          resources:
            requests:
              cpu: 20m
              memory: 204Mi
            limits:
              cpu: 200m
              memory: 2048Mi
          command: []
          args: []
          ports:
            - containerPort: 8080
              name: ${{ defaults.wiki_port_name }}
          imagePullPolicy: Always
          volumeMounts:
            - name: vn-data
              mountPath: /data
            - name: vn-appvn-repositories
              mountPath: /app/repositories
      volumes: []
  volumeClaimTemplates:
    - metadata:
        annotations:
          path: /data
          value: '1'
        name: vn-data
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: ${{ inputs.volume_size_data }}Gi
    - metadata:
        annotations:
          path: /app/repositories
          value: '3'
        name: vn-appvn-repositories
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: ${{ inputs.volume_size_repositories }}Gi

---
apiVersion: v1
kind: Service
metadata:
  name: ${{ defaults.app_name }}
  labels:
    cloud.sealos.io/app-deploy-manager: ${{ defaults.app_name }}
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: ${{ defaults.wiki_port_name }}
    protocol: TCP
  selector:
    app: ${{ defaults.app_name }}

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ${{ defaults.app_name }}
  labels:
    cloud.sealos.io/app-deploy-manager: ${{ defaults.app_name }}
    cloud.sealos.io/app-deploy-manager-domain: ${{ defaults.app_host }}
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: 32m
    nginx.ingress.kubernetes.io/ssl-redirect: 'false'
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/client-body-buffer-size: 64k
    nginx.ingress.kubernetes.io/proxy-buffer-size: 64k
    nginx.ingress.kubernetes.io/proxy-send-timeout: '300'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '300'
    nginx.ingress.kubernetes.io/server-snippet: |
      client_header_buffer_size 64k;
      large_client_header_buffers 4 128k;
spec:
  rules:
    - host: ${{ defaults.app_host }}.${{ SEALOS_CLOUD_DOMAIN }}
      http:
        paths:
          - pathType: Prefix
            path: /
            backend:
              service:
                name: ${{ defaults.app_name }}
                port:
                  number: 8080
  tls:
    - hosts:
        - ${{ defaults.app_host }}.${{ SEALOS_CLOUD_DOMAIN }}
      secretName: ${{ SEALOS_CERT_SECRET_NAME }}


---
apiVersion: v1
kind: Service
metadata:
  name: ${{ defaults.web_name }}
  labels:
    cloud.sealos.io/app-deploy-manager: ${{ defaults.web_name }}
spec:
  ports:
  - port: 3000
    targetPort: 3000
    name: ${{ defaults.web_port_name }}
    protocol: TCP
  selector:
    app: ${{ defaults.web_name }}

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${{ defaults.web_name }}
  annotations:
    originImageName: crpi-j9ha7sxwhatgtvj4.cn-shenzhen.personal.cr.aliyuncs.com/koala-ai/koala-wiki-web
    deploy.cloud.sealos.io/minReplicas: '1'
    deploy.cloud.sealos.io/maxReplicas: '1'
  labels:
    app: ${{ defaults.web_name }}
    cloud.sealos.io/app-deploy-manager: ${{ defaults.web_name }}
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app: ${{ defaults.web_name }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      labels:
        app: ${{ defaults.web_name }}
    spec:
      automountServiceAccountToken: false
      containers:
        - name: ${{ defaults.web_name }}
          image: crpi-j9ha7sxwhatgtvj4.cn-shenzhen.personal.cr.aliyuncs.com/koala-ai/koala-wiki-web
          env:
            - name: API_URL
              value: https://${{ defaults.app_host }}.${{ SEALOS_CLOUD_DOMAIN }}
          resources:
            requests:
              cpu: 20m
              memory: 25Mi
            limits:
              cpu: 200m
              memory: 256Mi
          ports:
            - containerPort: 3000
              name: ${{ defaults.web_port_name }}
          imagePullPolicy: Always
          volumeMounts: []
      volumes: []

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ${{ defaults.web_name }}
  labels:
    cloud.sealos.io/app-deploy-manager: ${{ defaults.web_name }}
    cloud.sealos.io/app-deploy-manager-domain: ${{ defaults.web_host }}
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: 32m
    nginx.ingress.kubernetes.io/ssl-redirect: 'false'
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/client-body-buffer-size: 64k
    nginx.ingress.kubernetes.io/proxy-buffer-size: 64k
    nginx.ingress.kubernetes.io/proxy-send-timeout: '300'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '300'
    nginx.ingress.kubernetes.io/server-snippet: |
      client_header_buffer_size 64k;
      large_client_header_buffers 4 128k;
spec:
  rules:
    - host: ${{ defaults.web_host }}.${{ SEALOS_CLOUD_DOMAIN }}
      http:
        paths:
          - pathType: Prefix
            path: /
            backend:
              service:
                name: ${{ defaults.web_name }}
                port:
                  number: 3000
  tls:
    - hosts:
        - ${{ defaults.web_host }}.${{ SEALOS_CLOUD_DOMAIN }}
      secretName: ${{ SEALOS_CERT_SECRET_NAME }}
