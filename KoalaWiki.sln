
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 18
VisualStudioVersion = 18.0.11010.61 d18.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{7F5745CD-DAE4-4C81-A675-B427B634EB17}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KoalaWiki", "src\KoalaWiki\KoalaWiki.csproj", "{7542FEE4-8528-4C9D-A26B-8C1F4A24307C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{25263893-E043-4F54-A41B-D97FACE64062}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		build-image.bat = build-image.bat
		build-image.sh = build-image.sh
		build.sh = build.sh
		Directory.Packages.props = Directory.Packages.props
		docker-compose-mem0.yml = docker-compose-mem0.yml
		docker-compose.yml = docker-compose.yml
		.github\workflows\docker-image.yml = .github\workflows\docker-image.yml
		NuGet.Config = NuGet.Config
		README.md = README.md
		README.zh-CN.md = README.zh-CN.md
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KoalaWiki.Core", "KoalaWiki.Core\KoalaWiki.Core.csproj", "{8E887B45-75E2-4264-B5DE-93C9E206495E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Provider", "Provider", "{E6A1B1C9-55F9-4568-9389-C16D47CFE798}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KoalaWiki.Provider.PostgreSQL", "Provider\KoalaWiki.Provider.PostgreSQL\KoalaWiki.Provider.PostgreSQL.csproj", "{A15824D3-1741-4402-8706-D44E40D08BE0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KoalaWiki.Provider.Sqlite", "Provider\KoalaWiki.Provider.Sqlite\KoalaWiki.Provider.Sqlite.csproj", "{31D03AC9-7F8F-47B4-8031-FAC443F1B44D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KoalaWiki.Domains", "KoalaWiki.Domains\KoalaWiki.Domains.csproj", "{72EF7275-1CC8-4609-8D4E-A77E5401C235}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KoalaWiki.Provider.SqlServer", "Provider\KoalaWiki.Provider.SqlServer\KoalaWiki.Provider.SqlServer.csproj", "{D6C334AD-D67D-4E20-83C5-B5B47BCB886E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KoalaWiki.AppHost", "src\KoalaWiki.AppHost\KoalaWiki.AppHost.csproj", "{B8B49C5F-535C-47AC-9D50-635CCA37E401}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KoalaWiki.ServiceDefaults", "src\KoalaWiki.ServiceDefaults\KoalaWiki.ServiceDefaults.csproj", "{A9C4E6B7-B9C3-6605-3477-4FA0194EDBA5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "framework", "framework", "{DCEA2329-4E7D-4E3E-A507-DEE82B7F7D8A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{F39528D6-BAFE-4C38-B5C6-39CEA5B1BEA9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OpenDeepWiki.CodeFoundation", "framework\src\OpenDeepWiki.CodeFoundation\OpenDeepWiki.CodeFoundation.csproj", "{27B59BA7-49B8-4B9B-BDA2-9530DD2AE3B8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KoalaWiki.Provider.MySQL", "Provider\KoalaWiki.Provider.MySQL\KoalaWiki.Provider.MySQL.csproj", "{E871B8A2-41D3-455A-AF74-6D324940A0AB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7542FEE4-8528-4C9D-A26B-8C1F4A24307C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7542FEE4-8528-4C9D-A26B-8C1F4A24307C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7542FEE4-8528-4C9D-A26B-8C1F4A24307C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7542FEE4-8528-4C9D-A26B-8C1F4A24307C}.Release|Any CPU.Build.0 = Release|Any CPU
		{8E887B45-75E2-4264-B5DE-93C9E206495E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8E887B45-75E2-4264-B5DE-93C9E206495E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8E887B45-75E2-4264-B5DE-93C9E206495E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8E887B45-75E2-4264-B5DE-93C9E206495E}.Release|Any CPU.Build.0 = Release|Any CPU
		{A15824D3-1741-4402-8706-D44E40D08BE0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A15824D3-1741-4402-8706-D44E40D08BE0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A15824D3-1741-4402-8706-D44E40D08BE0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A15824D3-1741-4402-8706-D44E40D08BE0}.Release|Any CPU.Build.0 = Release|Any CPU
		{31D03AC9-7F8F-47B4-8031-FAC443F1B44D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{31D03AC9-7F8F-47B4-8031-FAC443F1B44D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{31D03AC9-7F8F-47B4-8031-FAC443F1B44D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{31D03AC9-7F8F-47B4-8031-FAC443F1B44D}.Release|Any CPU.Build.0 = Release|Any CPU
		{72EF7275-1CC8-4609-8D4E-A77E5401C235}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{72EF7275-1CC8-4609-8D4E-A77E5401C235}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{72EF7275-1CC8-4609-8D4E-A77E5401C235}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{72EF7275-1CC8-4609-8D4E-A77E5401C235}.Release|Any CPU.Build.0 = Release|Any CPU
		{D6C334AD-D67D-4E20-83C5-B5B47BCB886E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D6C334AD-D67D-4E20-83C5-B5B47BCB886E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D6C334AD-D67D-4E20-83C5-B5B47BCB886E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D6C334AD-D67D-4E20-83C5-B5B47BCB886E}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8B49C5F-535C-47AC-9D50-635CCA37E401}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8B49C5F-535C-47AC-9D50-635CCA37E401}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8B49C5F-535C-47AC-9D50-635CCA37E401}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8B49C5F-535C-47AC-9D50-635CCA37E401}.Release|Any CPU.Build.0 = Release|Any CPU
		{A9C4E6B7-B9C3-6605-3477-4FA0194EDBA5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A9C4E6B7-B9C3-6605-3477-4FA0194EDBA5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A9C4E6B7-B9C3-6605-3477-4FA0194EDBA5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A9C4E6B7-B9C3-6605-3477-4FA0194EDBA5}.Release|Any CPU.Build.0 = Release|Any CPU
		{27B59BA7-49B8-4B9B-BDA2-9530DD2AE3B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{27B59BA7-49B8-4B9B-BDA2-9530DD2AE3B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{27B59BA7-49B8-4B9B-BDA2-9530DD2AE3B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{27B59BA7-49B8-4B9B-BDA2-9530DD2AE3B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{E871B8A2-41D3-455A-AF74-6D324940A0AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E871B8A2-41D3-455A-AF74-6D324940A0AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E871B8A2-41D3-455A-AF74-6D324940A0AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E871B8A2-41D3-455A-AF74-6D324940A0AB}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{7542FEE4-8528-4C9D-A26B-8C1F4A24307C} = {7F5745CD-DAE4-4C81-A675-B427B634EB17}
		{8E887B45-75E2-4264-B5DE-93C9E206495E} = {7F5745CD-DAE4-4C81-A675-B427B634EB17}
		{E6A1B1C9-55F9-4568-9389-C16D47CFE798} = {7F5745CD-DAE4-4C81-A675-B427B634EB17}
		{A15824D3-1741-4402-8706-D44E40D08BE0} = {E6A1B1C9-55F9-4568-9389-C16D47CFE798}
		{31D03AC9-7F8F-47B4-8031-FAC443F1B44D} = {E6A1B1C9-55F9-4568-9389-C16D47CFE798}
		{72EF7275-1CC8-4609-8D4E-A77E5401C235} = {7F5745CD-DAE4-4C81-A675-B427B634EB17}
		{D6C334AD-D67D-4E20-83C5-B5B47BCB886E} = {E6A1B1C9-55F9-4568-9389-C16D47CFE798}
		{F39528D6-BAFE-4C38-B5C6-39CEA5B1BEA9} = {DCEA2329-4E7D-4E3E-A507-DEE82B7F7D8A}
		{27B59BA7-49B8-4B9B-BDA2-9530DD2AE3B8} = {F39528D6-BAFE-4C38-B5C6-39CEA5B1BEA9}
		{E871B8A2-41D3-455A-AF74-6D324940A0AB} = {E6A1B1C9-55F9-4568-9389-C16D47CFE798}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {57749C22-AF12-4764-9AD7-5327DB50CDE5}
	EndGlobalSection
EndGlobal
