// <auto-generated />
using System;
using KoalaWiki.Provider.SqlServer;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace KoalaWiki.Provider.SqlServer.Migrations
{
    [DbContext(typeof(SqlServerContext))]
    [Migration("20250707100007_postgres")]
    partial class postgres
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("KoalaWiki.Domains.AppConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<string>("AllowedDomainsJson")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("允许的域名列表JSON");

                    b.Property<string>("AppId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasComment("应用ID");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("应用描述");

                    b.Property<bool>("EnableDomainValidation")
                        .HasColumnType("bit")
                        .HasComment("是否启用域名验证");

                    b.Property<string>("Introduction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit")
                        .HasComment("是否启用");

                    b.Property<DateTime?>("LastUsedAt")
                        .HasColumnType("datetime2")
                        .HasComment("最后使用时间");

                    b.Property<string>("Mcps")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Model")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("应用名称");

                    b.Property<string>("OrganizationName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("组织名称");

                    b.Property<string>("Prompt")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecommendedQuestions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RepositoryName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("仓库名称");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("创建用户ID");

                    b.HasKey("Id");

                    b.HasIndex("AppId")
                        .IsUnique();

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsEnabled");

                    b.HasIndex("Name");

                    b.HasIndex("OrganizationName");

                    b.HasIndex("RepositoryName");

                    b.HasIndex("UserId");

                    b.HasIndex("OrganizationName", "RepositoryName");

                    b.ToTable("AppConfigs", t =>
                        {
                            t.HasComment("应用配置表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Document", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<long>("CommentCount")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("文档描述");

                    b.Property<string>("GitPath")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("datetime2");

                    b.Property<long>("LikeCount")
                        .HasColumnType("bigint");

                    b.Property<byte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<string>("WarehouseId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("所属仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("WarehouseId");

                    b.ToTable("Documents", t =>
                        {
                            t.HasComment("文档表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.DocumentCatalog", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<DateTime?>("DeletedTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("目录描述");

                    b.Property<string>("DucumentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("文档Id");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasComment("是否已删除");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("目录名称");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("ParentId")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("父级目录Id");

                    b.Property<string>("Prompt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WarehouseId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("所属仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DucumentId");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Name");

                    b.HasIndex("ParentId");

                    b.HasIndex("WarehouseId");

                    b.ToTable("DocumentCatalogs", t =>
                        {
                            t.HasComment("文档目录表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.DocumentCommitRecord", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<string>("Author")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("作者");

                    b.Property<string>("CommitId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("提交Id");

                    b.Property<string>("CommitMessage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("提交信息");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WarehouseId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("CommitId");

                    b.HasIndex("WarehouseId");

                    b.ToTable("DocumentCommitRecords", t =>
                        {
                            t.HasComment("文档提交记录表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.DocumentFile.DocumentFileItem", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<long>("CommentCount")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("文件描述");

                    b.Property<string>("DocumentCatalogId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("目录Id");

                    b.Property<string>("Extra")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("扩展信息");

                    b.Property<bool>("IsEmbedded")
                        .HasColumnType("bit");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("元数据");

                    b.Property<int>("RequestToken")
                        .HasColumnType("int");

                    b.Property<int>("ResponseToken")
                        .HasColumnType("int");

                    b.Property<long>("Size")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("文件标题");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DocumentCatalogId");

                    b.HasIndex("Title");

                    b.ToTable("DocumentFileItems", t =>
                        {
                            t.HasComment("文档文件项表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.DocumentFile.DocumentFileItemSource", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<string>("DocumentFileItemId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("文件项Id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("来源名称");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DocumentFileItemId");

                    b.HasIndex("Name");

                    b.ToTable("DocumentFileItemSources", t =>
                        {
                            t.HasComment("文档文件项来源表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.FineTuning.FineTuningTask", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<string>("Dataset")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DocumentCatalogId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("目录Id");

                    b.Property<string>("Error")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("微调任务名称");

                    b.Property<string>("OriginalDataset")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasComment("任务状态");

                    b.Property<string>("TrainingDatasetId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("训练数据集Id");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("用户Id");

                    b.Property<string>("WarehouseId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DocumentCatalogId");

                    b.HasIndex("Name");

                    b.HasIndex("Status");

                    b.HasIndex("TrainingDatasetId");

                    b.HasIndex("UserId");

                    b.HasIndex("WarehouseId");

                    b.ToTable("FineTuningTasks", t =>
                        {
                            t.HasComment("微调任务表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.FineTuning.TrainingDataset", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<string>("ApiKey")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<string>("Endpoint")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("数据集名称");

                    b.Property<string>("Prompt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WarehouseId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Name");

                    b.HasIndex("WarehouseId");

                    b.ToTable("TrainingDatasets", t =>
                        {
                            t.HasComment("训练数据集表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.MCP.MCPHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<string>("Answer")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("CostTime")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<string>("Ip")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Question")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserAgent")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("用户Id");

                    b.Property<string>("WarehouseId")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("UserId");

                    b.HasIndex("WarehouseId");

                    b.ToTable("MCPHistories", t =>
                        {
                            t.HasComment("MCP历史记录");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.MiniMap", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("小地图数据");

                    b.Property<string>("WarehouseId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("WarehouseId");

                    b.ToTable("MiniMaps", t =>
                        {
                            t.HasComment("小地图表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Statistics.AccessRecord", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("IP地址");

                    b.Property<string>("Method")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("请求方法");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("访问路径");

                    b.Property<string>("ResourceId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("资源Id");

                    b.Property<string>("ResourceType")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("资源类型");

                    b.Property<long>("ResponseTime")
                        .HasColumnType("bigint")
                        .HasComment("响应时间");

                    b.Property<int>("StatusCode")
                        .HasColumnType("int")
                        .HasComment("状态码");

                    b.Property<string>("UserAgent")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("用户代理");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("用户Id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IpAddress");

                    b.HasIndex("ResourceId");

                    b.HasIndex("ResourceType");

                    b.HasIndex("UserId");

                    b.HasIndex("ResourceType", "ResourceId");

                    b.ToTable("AccessRecords", t =>
                        {
                            t.HasComment("访问记录表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Statistics.DailyStatistics", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<int>("ActiveUsers")
                        .HasColumnType("int")
                        .HasComment("活跃用户数");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2")
                        .HasComment("统计日期");

                    b.Property<int>("NewDocumentsCount")
                        .HasColumnType("int")
                        .HasComment("新增文档数");

                    b.Property<int>("NewRepositoriesCount")
                        .HasColumnType("int")
                        .HasComment("新增仓库数");

                    b.Property<int>("NewUsersCount")
                        .HasColumnType("int")
                        .HasComment("新增用户数");

                    b.Property<int>("PageViews")
                        .HasColumnType("int")
                        .HasComment("页面访问量");

                    b.Property<int>("UniqueVisitors")
                        .HasColumnType("int")
                        .HasComment("独立访问用户数");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("更新时间");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Date")
                        .IsUnique();

                    b.ToTable("DailyStatistics", t =>
                        {
                            t.HasComment("每日统计表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Users.Role", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("角色描述");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSystemRole")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("角色名称");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Name");

                    b.ToTable("Roles", t =>
                        {
                            t.HasComment("角色表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Users.User", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<string>("Avatar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("邮箱");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("datetime2")
                        .HasComment("最后登录时间");

                    b.Property<string>("LastLoginIp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("用户名");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("密码");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Email");

                    b.HasIndex("LastLoginAt");

                    b.HasIndex("Name");

                    b.ToTable("Users", t =>
                        {
                            t.HasComment("用户表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Users.UserInAuth", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<string>("AuthId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("认证Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("认证提供方");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("用户Id");

                    b.HasKey("Id");

                    b.HasIndex("AuthId");

                    b.HasIndex("Provider");

                    b.HasIndex("UserId");

                    b.ToTable("UserInAuths", t =>
                        {
                            t.HasComment("用户认证表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Users.UserInRole", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("用户Id");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("角色Id");

                    b.HasKey("UserId", "RoleId");

                    b.ToTable("UserInRoles", t =>
                        {
                            t.HasComment("用户角色关联表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Warehouse.Warehouse", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("仓库地址");

                    b.Property<string>("Branch")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("分支");

                    b.Property<int?>("Classify")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasComment("创建时间");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("仓库描述");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Error")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GitPassword")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GitUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsEmbedded")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRecommended")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("仓库名称");

                    b.Property<string>("OptimizedDirectoryStructure")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrganizationName")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("组织名称");

                    b.Property<string>("Prompt")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Readme")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("Status")
                        .HasColumnType("tinyint")
                        .HasComment("仓库状态");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("仓库类型");

                    b.Property<string>("Version")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("Address");

                    b.HasIndex("Branch");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Name");

                    b.HasIndex("OrganizationName");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.ToTable("Warehouses", t =>
                        {
                            t.HasComment("知识仓库表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Warehouse.WarehouseInRole", b =>
                {
                    b.Property<string>("WarehouseId")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("仓库Id");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("角色Id");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("bit");

                    b.Property<bool>("IsReadOnly")
                        .HasColumnType("bit");

                    b.Property<bool>("IsWrite")
                        .HasColumnType("bit");

                    b.HasKey("WarehouseId", "RoleId");

                    b.HasIndex("RoleId");

                    b.HasIndex("WarehouseId");

                    b.ToTable("WarehouseInRoles", t =>
                        {
                            t.HasComment("仓库角色关联表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Entities.DocumentOverview", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("主键Id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DocumentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("文档Id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasComment("文档标题");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.HasIndex("Title");

                    b.ToTable("DocumentOverviews", t =>
                        {
                            t.HasComment("文档概览表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.DocumentFile.DocumentFileItemSource", b =>
                {
                    b.HasOne("KoalaWiki.Domains.DocumentFile.DocumentFileItem", "DocumentFileItem")
                        .WithMany()
                        .HasForeignKey("DocumentFileItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentFileItem");
                });

            modelBuilder.Entity("KoalaWiki.Domains.FineTuning.FineTuningTask", b =>
                {
                    b.HasOne("KoalaWiki.Domains.DocumentCatalog", "DocumentCatalog")
                        .WithMany()
                        .HasForeignKey("DocumentCatalogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentCatalog");
                });
#pragma warning restore 612, 618
        }
    }
}
