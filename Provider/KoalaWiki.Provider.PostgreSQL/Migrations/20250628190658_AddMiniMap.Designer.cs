// <auto-generated />
using System;
using KoalaWiki.Provider.PostgreSQL;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace KoalaWiki.Provider.PostgreSQL.Migrations
{
    [DbContext(typeof(PostgreSQLContext))]
    [Migration("20250628190658_AddMiniMap")]
    partial class AddMiniMap
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("KoalaWiki.Domains.AppConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<string>("AllowedDomainsJson")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("允许的域名列表JSON");

                    b.Property<string>("AppId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasComment("应用ID");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasComment("应用描述");

                    b.Property<bool>("EnableDomainValidation")
                        .HasColumnType("boolean")
                        .HasComment("是否启用域名验证");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasComment("是否启用");

                    b.Property<DateTime?>("LastUsedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("最后使用时间");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasComment("应用名称");

                    b.Property<string>("OrganizationName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasComment("组织名称");

                    b.Property<string>("RepositoryName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasComment("仓库名称");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("创建用户ID");

                    b.HasKey("Id");

                    b.HasIndex("AppId")
                        .IsUnique();

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsEnabled");

                    b.HasIndex("Name");

                    b.HasIndex("OrganizationName");

                    b.HasIndex("RepositoryName");

                    b.HasIndex("UserId");

                    b.HasIndex("OrganizationName", "RepositoryName");

                    b.ToTable("AppConfigs", t =>
                        {
                            t.HasComment("应用配置表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Document", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<long>("CommentCount")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("文档描述");

                    b.Property<string>("GitPath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<long>("LikeCount")
                        .HasColumnType("bigint");

                    b.Property<byte>("Status")
                        .HasColumnType("smallint");

                    b.Property<string>("WarehouseId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("所属仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("WarehouseId");

                    b.ToTable("Documents", t =>
                        {
                            t.HasComment("文档表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.DocumentCatalog", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<DateTime?>("DeletedTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("目录描述");

                    b.Property<string>("DucumentId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("文档Id");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasComment("是否已删除");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("目录名称");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("ParentId")
                        .HasColumnType("text")
                        .HasComment("父级目录Id");

                    b.Property<string>("Prompt")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("WarehouseId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("所属仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DucumentId");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Name");

                    b.HasIndex("ParentId");

                    b.HasIndex("WarehouseId");

                    b.ToTable("DocumentCatalogs", t =>
                        {
                            t.HasComment("文档目录表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.DocumentCommitRecord", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<string>("Author")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("作者");

                    b.Property<string>("CommitId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("提交Id");

                    b.Property<string>("CommitMessage")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("提交信息");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("WarehouseId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("CommitId");

                    b.HasIndex("WarehouseId");

                    b.ToTable("DocumentCommitRecords", t =>
                        {
                            t.HasComment("文档提交记录表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.DocumentFile.DocumentFileItem", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<long>("CommentCount")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("文件描述");

                    b.Property<string>("DocumentCatalogId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("目录Id");

                    b.Property<string>("Extra")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("扩展信息");

                    b.Property<bool>("IsEmbedded")
                        .HasColumnType("boolean");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("元数据");

                    b.Property<int>("RequestToken")
                        .HasColumnType("integer");

                    b.Property<int>("ResponseToken")
                        .HasColumnType("integer");

                    b.Property<long>("Size")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("文件标题");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DocumentCatalogId");

                    b.HasIndex("Title");

                    b.ToTable("DocumentFileItems", t =>
                        {
                            t.HasComment("文档文件项表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.DocumentFile.DocumentFileItemSource", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<string>("DocumentFileItemId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("文件项Id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("来源名称");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DocumentFileItemId");

                    b.HasIndex("Name");

                    b.ToTable("DocumentFileItemSources", t =>
                        {
                            t.HasComment("文档文件项来源表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.FineTuning.FineTuningTask", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<string>("Dataset")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DocumentCatalogId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("目录Id");

                    b.Property<string>("Error")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("微调任务名称");

                    b.Property<string>("OriginalDataset")
                        .HasColumnType("text");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasComment("任务状态");

                    b.Property<string>("TrainingDatasetId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("训练数据集Id");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("用户Id");

                    b.Property<string>("WarehouseId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DocumentCatalogId");

                    b.HasIndex("Name");

                    b.HasIndex("Status");

                    b.HasIndex("TrainingDatasetId");

                    b.HasIndex("UserId");

                    b.HasIndex("WarehouseId");

                    b.ToTable("FineTuningTasks", t =>
                        {
                            t.HasComment("微调任务表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.FineTuning.TrainingDataset", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<string>("ApiKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<string>("Endpoint")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("数据集名称");

                    b.Property<string>("Prompt")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("WarehouseId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Name");

                    b.HasIndex("WarehouseId");

                    b.ToTable("TrainingDatasets", t =>
                        {
                            t.HasComment("训练数据集表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.MCP.MCPHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<string>("Answer")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("CostTime")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<string>("Ip")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Question")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UserAgent")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text")
                        .HasComment("用户Id");

                    b.Property<string>("WarehouseId")
                        .HasColumnType("text")
                        .HasComment("仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("UserId");

                    b.HasIndex("WarehouseId");

                    b.ToTable("MCPHistories", t =>
                        {
                            t.HasComment("MCP历史记录");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.MiniMap", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("小地图数据");

                    b.Property<string>("WarehouseId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("仓库Id");

                    b.HasKey("Id");

                    b.HasIndex("WarehouseId");

                    b.ToTable("MiniMaps", t =>
                        {
                            t.HasComment("小地图表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Statistics.AccessRecord", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("IP地址");

                    b.Property<string>("Method")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("请求方法");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("访问路径");

                    b.Property<string>("ResourceId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("资源Id");

                    b.Property<string>("ResourceType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("资源类型");

                    b.Property<long>("ResponseTime")
                        .HasColumnType("bigint")
                        .HasComment("响应时间");

                    b.Property<int>("StatusCode")
                        .HasColumnType("integer")
                        .HasComment("状态码");

                    b.Property<string>("UserAgent")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("用户代理");

                    b.Property<string>("UserId")
                        .HasColumnType("text")
                        .HasComment("用户Id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IpAddress");

                    b.HasIndex("ResourceId");

                    b.HasIndex("ResourceType");

                    b.HasIndex("UserId");

                    b.HasIndex("ResourceType", "ResourceId");

                    b.ToTable("AccessRecords", t =>
                        {
                            t.HasComment("访问记录表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Statistics.DailyStatistics", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<int>("ActiveUsers")
                        .HasColumnType("integer")
                        .HasComment("活跃用户数");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("统计日期");

                    b.Property<int>("NewDocumentsCount")
                        .HasColumnType("integer")
                        .HasComment("新增文档数");

                    b.Property<int>("NewRepositoriesCount")
                        .HasColumnType("integer")
                        .HasComment("新增仓库数");

                    b.Property<int>("NewUsersCount")
                        .HasColumnType("integer")
                        .HasComment("新增用户数");

                    b.Property<int>("PageViews")
                        .HasColumnType("integer")
                        .HasComment("页面访问量");

                    b.Property<int>("UniqueVisitors")
                        .HasColumnType("integer")
                        .HasComment("独立访问用户数");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("更新时间");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Date")
                        .IsUnique();

                    b.ToTable("DailyStatistics", t =>
                        {
                            t.HasComment("每日统计表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Users.Role", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("角色描述");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSystemRole")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("角色名称");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Name");

                    b.ToTable("Roles", t =>
                        {
                            t.HasComment("角色表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Users.User", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<string>("Avatar")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("邮箱");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("最后登录时间");

                    b.Property<string>("LastLoginIp")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("用户名");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("密码");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Email");

                    b.HasIndex("LastLoginAt");

                    b.HasIndex("Name");

                    b.ToTable("Users", t =>
                        {
                            t.HasComment("用户表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Users.UserInAuth", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<string>("AuthId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("认证Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("认证提供方");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("用户Id");

                    b.HasKey("Id");

                    b.HasIndex("AuthId");

                    b.HasIndex("Provider");

                    b.HasIndex("UserId");

                    b.ToTable("UserInAuths", t =>
                        {
                            t.HasComment("用户认证表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Users.UserInRole", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text")
                        .HasComment("用户Id");

                    b.Property<string>("RoleId")
                        .HasColumnType("text")
                        .HasComment("角色Id");

                    b.HasKey("UserId", "RoleId");

                    b.ToTable("UserInRoles", t =>
                        {
                            t.HasComment("用户角色关联表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Warehouse.Warehouse", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("仓库地址");

                    b.Property<string>("Branch")
                        .HasColumnType("text")
                        .HasComment("分支");

                    b.Property<int?>("Classify")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("创建时间");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("仓库描述");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Error")
                        .HasColumnType("text");

                    b.Property<string>("GitPassword")
                        .HasColumnType("text");

                    b.Property<string>("GitUserName")
                        .HasColumnType("text");

                    b.Property<bool>("IsEmbedded")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRecommended")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("仓库名称");

                    b.Property<string>("OptimizedDirectoryStructure")
                        .HasColumnType("text");

                    b.Property<string>("OrganizationName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("组织名称");

                    b.Property<string>("Prompt")
                        .HasColumnType("text");

                    b.Property<string>("Readme")
                        .HasColumnType("text");

                    b.Property<byte>("Status")
                        .HasColumnType("smallint")
                        .HasComment("仓库状态");

                    b.Property<string>("Type")
                        .HasColumnType("text")
                        .HasComment("仓库类型");

                    b.Property<string>("Version")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Address");

                    b.HasIndex("Branch");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Name");

                    b.HasIndex("OrganizationName");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.ToTable("Warehouses", t =>
                        {
                            t.HasComment("知识仓库表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.Warehouse.WarehouseInRole", b =>
                {
                    b.Property<string>("WarehouseId")
                        .HasColumnType("text")
                        .HasComment("仓库Id");

                    b.Property<string>("RoleId")
                        .HasColumnType("text")
                        .HasComment("角色Id");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsReadOnly")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsWrite")
                        .HasColumnType("boolean");

                    b.HasKey("WarehouseId", "RoleId");

                    b.HasIndex("RoleId");

                    b.HasIndex("WarehouseId");

                    b.ToTable("WarehouseInRoles", t =>
                        {
                            t.HasComment("仓库角色关联表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Entities.DocumentOverview", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasComment("主键Id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("DocumentId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("文档Id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("文档标题");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.HasIndex("Title");

                    b.ToTable("DocumentOverviews", t =>
                        {
                            t.HasComment("文档概览表");
                        });
                });

            modelBuilder.Entity("KoalaWiki.Domains.DocumentFile.DocumentFileItemSource", b =>
                {
                    b.HasOne("KoalaWiki.Domains.DocumentFile.DocumentFileItem", "DocumentFileItem")
                        .WithMany()
                        .HasForeignKey("DocumentFileItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentFileItem");
                });

            modelBuilder.Entity("KoalaWiki.Domains.FineTuning.FineTuningTask", b =>
                {
                    b.HasOne("KoalaWiki.Domains.DocumentCatalog", "DocumentCatalog")
                        .WithMany()
                        .HasForeignKey("DocumentCatalogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentCatalog");
                });
#pragma warning restore 612, 618
        }
    }
}
