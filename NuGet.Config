<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <packageSources>
        <clear />
        <add key="nuget.org" value="https://api.nuget.org/v3/index.json" allowInsecureConnections="True" />
        <add key="azure" value="https://nuget.cdn.azure.cn/v3/index.json" allowInsecureConnections="True" />
    </packageSources>
    <packageRestore>
        <add key="enabled" value="True" />
        <add key="automatic" value="True" />
    </packageRestore>
    <bindingRedirects>
        <add key="skip" value="False" />
    </bindingRedirects>
</configuration>