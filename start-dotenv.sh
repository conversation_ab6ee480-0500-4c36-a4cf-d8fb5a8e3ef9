#!/bin/bash

# KoalaWiki Dotenv Startup Script
# This script starts the backend and frontend with environment variable configuration

echo "🚀 Starting KoalaWiki with dotenv configuration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env files exist
print_status "Checking environment configuration..."

# Backend .env file
BACKEND_ENV_FILE="src/KoalaWiki/.env"
if [ ! -f "$BACKEND_ENV_FILE" ]; then
    if [ -f "src/KoalaWiki/.env copy" ]; then
        print_warning "Backend .env not found, copying from .env copy"
        cp "src/KoalaWiki/.env copy" "$BACKEND_ENV_FILE"
        print_status "Created backend .env file"
    else
        print_error "Backend .env file not found. Please create src/KoalaWiki/.env"
        exit 1
    fi
else
    print_status "Backend .env file found"
fi

# Frontend .env.local file
FRONTEND_ENV_FILE="web/.env.local"
if [ ! -f "$FRONTEND_ENV_FILE" ]; then
    if [ -f "web/env.example" ]; then
        print_warning "Frontend .env.local not found, copying from env.example"
        cp "web/env.example" "$FRONTEND_ENV_FILE"
        # Remove the comment lines for the actual .env.local
        sed -i '' '/^#/d' "$FRONTEND_ENV_FILE"
        sed -i '' '/^$/d' "$FRONTEND_ENV_FILE"
        print_status "Created frontend .env.local file"
    else
        print_warning "Frontend .env.local not found, creating default"
        echo "NEXT_PUBLIC_API_URL=http://localhost:5085" > "$FRONTEND_ENV_FILE"
        print_status "Created frontend .env.local file with default settings"
    fi
else
    print_status "Frontend .env.local file found"
fi

# Function to cleanup background processes
cleanup() {
    print_warning "Shutting down services..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        print_status "Backend stopped"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        print_status "Frontend stopped"
    fi
    exit 0
}

# Set up signal handling
trap cleanup SIGINT SIGTERM

# Start backend
print_status "Starting backend service..."
cd src/KoalaWiki

# Check if the backend is built
if [ ! -f "bin/Debug/net9.0/KoalaWiki.dll" ]; then
    print_status "Building backend..."
    dotnet build
    if [ $? -ne 0 ]; then
        print_error "Backend build failed"
        exit 1
    fi
fi

# Start backend with dotenv support
print_status "Backend starting on http://localhost:5085"
dotnet run --no-build &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 5

# Go back to root directory
cd ../..

# Start frontend
print_status "Starting frontend service..."
cd web

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    print_status "Installing frontend dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        print_error "Frontend dependency installation failed"
        kill $BACKEND_PID 2>/dev/null
        exit 1
    fi
fi

# Start frontend
print_status "Frontend starting on http://localhost:3000"
npm run dev &
FRONTEND_PID=$!

# Print startup information
echo ""
print_status "🎉 KoalaWiki services started successfully!"
echo -e "${BLUE}Backend:${NC}  http://localhost:5085"
echo -e "${BLUE}Frontend:${NC} http://localhost:3000"
echo ""
print_status "Environment variables loaded from:"
echo -e "  - Backend:  ${BACKEND_ENV_FILE}"
echo -e "  - Frontend: ${FRONTEND_ENV_FILE}"
echo ""
print_warning "Press Ctrl+C to stop all services"

# Wait for background processes
wait

