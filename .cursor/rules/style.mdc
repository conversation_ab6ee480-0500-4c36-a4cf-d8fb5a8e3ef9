---
alwaysApply: true
---
<style_guidelines>
  <visual_elements>
    <colors>
      Use monochromatic or limited color palettes (2-3 colors maximum) with high contrast
      Prefer flat, solid colors without gradients or textures
      Employ vibrant colors strategically to highlight important elements
    </colors>
    
    <typography>
      Use clean sans-serif fonts with clear hierarchy
      Maintain consistent font weights and sizes for similar elements
      Prioritize readability with appropriate line height and spacing
    </typography>
    
    <spacing>
      Embrace generous negative space
      Maintain consistent margins and padding using design tokens
      Align elements to a clear underlying grid system
    </spacing>
    
    <shapes>
      Use simple geometric shapes with zero or minimal shadows
      Avoid 3D effects, heavy shadows, or realistic lighting
      Utilize two-dimensional elements for icons and illustrations
    </shapes>
  </visual_elements>
  
  <design_principles>
    <simplicity>
      Remove all non-essential elements
      Focus on content and functionality over decoration
      Two-dimensional approach that avoids illusion of depth
    </simplicity>
    
    <clarity>
      Prioritize readability and functional communication
      Create clear visual hierarchy through size and weight
      Ensure all interactive elements are easily identifiable
    </clarity>
    
    <hierarchy>
      Establish clear information hierarchy through layout
      Use size, weight, and spacing to indicate importance
      Maintain consistent visual language across components
    </hierarchy>
    
    <consistency>
      Apply uniform treatment to similar elements
      Use design tokens for colors, spacing, and typography
      Create reusable components for common UI patterns
    </consistency>
  </design_principles>
  
  <constraints>
    <avoid>
      <decoration>No gradients, textures, shadows, or skeuomorphic elements</decoration>
      <complexity>No ornate borders, complex patterns, or excessive details</complexity>
      <depth>Avoid 3D effects, heavy shadows, or realistic lighting</depth>
      <verbosity>Use concise language; eliminate redundant expressions</verbosity>
      <animation>Minimize animations; use subtle transitions only when necessary</animation>
    </avoid>
    
    <embrace>
      <grid_systems>Align elements to a clear underlying grid</grid_systems>
      <whitespace>Use generous and intentional whitespace</whitespace>
      <iconography>Use simple, recognizable iconography when needed</iconography>
      <precision>Be precise and intentional with every element</precision>
      <responsiveness>Design for multiple screen sizes and contexts</responsiveness>
    </embrace>
  </constraints>
  
  <react_typescript_best_practices>
    <code_organization>
      <component_structure>
        Use functional components with hooks instead of class components
        Break down complex components into smaller, reusable ones
        Follow single responsibility principle for each component
      </component_structure>
      
      <naming_conventions>
        Use PascalCase for component names (Button, Modal, Card)
        Use camelCase for variables, functions, and instances
        Use descriptive names that indicate purpose and usage
      </naming_conventions>
      
      <folder_structure>
        Organize by features or routes rather than file types
        Keep related files (component, styles, tests) together
        Create shared components for reusable UI elements
      </folder_structure>
    </code_organization>
    
    <typescript_patterns>
      <type_safety>
        Enable strict mode in tsconfig.json
        Define explicit interfaces/types for props and state
        Avoid using 'any' type; prefer 'unknown' when needed
      </type_safety>
      
      <component_typing>
        Type component props with interfaces:
        interface ButtonProps {
          label: string;
          onClick: () => void;
          variant?: 'primary' | 'secondary';
        }
        
        Use React.FC sparingly, prefer explicit return types:
        const Button = ({ label, onClick, variant = 'primary' }: ButtonProps): JSX.Element => {
          return (
            <button className={variant} onClick={onClick}>{label}</button>
          );
        };
      </component_typing>
      
      <hooks_typing>
        Properly type useState:
        const [count, setCount] = useState<number>(0);
        
        Type event handlers properly:
        const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
          setValue(e.target.value);
        };
      </hooks_typing>
    </typescript_patterns>
    
    <performance_optimization>
      <rendering>
        Use React.memo for pure components to prevent unnecessary renders
        Utilize useMemo for expensive calculations
        Apply useCallback for event handlers passed to child components
      </rendering>
      
      <state_management>
        Keep state as local as possible
        Use context API for global state when needed
        Consider atomic state updates for complex state objects
      </state_management>
      
      <code_splitting>
        Implement lazy loading for routes and large components
        Use dynamic imports for code splitting
        Optimize bundle size with proper tree shaking
      </code_splitting>
    </performance_optimization>
  </react_typescript_best_practices>
  
  <output_format>
    <structure>
      <format>{Specified format based on request type}</format>
      <components>Only include essential components that serve clear functions</components>
      <responsiveness>Ensure design scales across different devices and contexts</responsiveness>
    </structure>
    
    <implementation>
      <styling>
        Prefer CSS-in-JS or CSS Modules for component-scoped styles
        Use utility-first CSS for consistent styling patterns
        Implement design tokens for colors, spacing, and typography
      </styling>
      
      <accessibility>
        Ensure proper contrast ratios for text and interactive elements
        Include appropriate ARIA attributes for custom components
        Support keyboard navigation for all interactive elements
      </accessibility>
      
      <performance>
        Optimize rendering with proper key props for lists
        Minimize DOM manipulation and reflows
        Implement virtualization for long lists
      </performance>
    </implementation>
  </output_format>
</style_guidelines><style_guidelines>
  <visual_elements>
    <colors>
      Use monochromatic or limited color palettes (2-3 colors maximum) with high contrast
      Prefer flat, solid colors without gradients or textures
      Employ vibrant colors strategically to highlight important elements
    </colors>
    
    <typography>
      Use clean sans-serif fonts with clear hierarchy
      Maintain consistent font weights and sizes for similar elements
      Prioritize readability with appropriate line height and spacing
    </typography>
    
    <spacing>
      Embrace generous negative space
      Maintain consistent margins and padding using design tokens
      Align elements to a clear underlying grid system
    </spacing>
    
    <shapes>
      Use simple geometric shapes with zero or minimal shadows
      Avoid 3D effects, heavy shadows, or realistic lighting
      Utilize two-dimensional elements for icons and illustrations
    </shapes>
  </visual_elements>
  
  <design_principles>
    <simplicity>
      Remove all non-essential elements
      Focus on content and functionality over decoration
      Two-dimensional approach that avoids illusion of depth
    </simplicity>
    
    <clarity>
      Prioritize readability and functional communication
      Create clear visual hierarchy through size and weight
      Ensure all interactive elements are easily identifiable
    </clarity>
    
    <hierarchy>
      Establish clear information hierarchy through layout
      Use size, weight, and spacing to indicate importance
      Maintain consistent visual language across components
    </hierarchy>
    
    <consistency>
      Apply uniform treatment to similar elements
      Use design tokens for colors, spacing, and typography
      Create reusable components for common UI patterns
    </consistency>
  </design_principles>
  
  <constraints>
    <avoid>
      <decoration>No gradients, textures, shadows, or skeuomorphic elements</decoration>
      <complexity>No ornate borders, complex patterns, or excessive details</complexity>
      <depth>Avoid 3D effects, heavy shadows, or realistic lighting</depth>
      <verbosity>Use concise language; eliminate redundant expressions</verbosity>
      <animation>Minimize animations; use subtle transitions only when necessary</animation>
    </avoid>
    
    <embrace>
      <grid_systems>Align elements to a clear underlying grid</grid_systems>
      <whitespace>Use generous and intentional whitespace</whitespace>
      <iconography>Use simple, recognizable iconography when needed</iconography>
      <precision>Be precise and intentional with every element</precision>
      <responsiveness>Design for multiple screen sizes and contexts</responsiveness>
    </embrace>
  </constraints>
  
  <react_typescript_best_practices>
    <code_organization>
      <component_structure>
        Use functional components with hooks instead of class components
        Break down complex components into smaller, reusable ones
        Follow single responsibility principle for each component
      </component_structure>
      
      <naming_conventions>
        Use PascalCase for component names (Button, Modal, Card)
        Use camelCase for variables, functions, and instances
        Use descriptive names that indicate purpose and usage
      </naming_conventions>
      
      <folder_structure>
        Organize by features or routes rather than file types
        Keep related files (component, styles, tests) together
        Create shared components for reusable UI elements
      </folder_structure>
    </code_organization>
    
    <typescript_patterns>
      <type_safety>
        Enable strict mode in tsconfig.json
        Define explicit interfaces/types for props and state
        Avoid using 'any' type; prefer 'unknown' when needed
      </type_safety>
      
      <component_typing>
        Type component props with interfaces:
        interface ButtonProps {
          label: string;
          onClick: () => void;
          variant?: 'primary' | 'secondary';
        }
        
        Use React.FC sparingly, prefer explicit return types:
        const Button = ({ label, onClick, variant = 'primary' }: ButtonProps): JSX.Element => {
          return (
            <button className={variant} onClick={onClick}>{label}</button>
          );
        };
      </component_typing>
      
      <hooks_typing>
        Properly type useState:
        const [count, setCount] = useState<number>(0);
        
        Type event handlers properly:
        const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
          setValue(e.target.value);
        };
      </hooks_typing>
    </typescript_patterns>
    
    <performance_optimization>
      <rendering>
        Use React.memo for pure components to prevent unnecessary renders
        Utilize useMemo for expensive calculations
        Apply useCallback for event handlers passed to child components
      </rendering>
      
      <state_management>
        Keep state as local as possible
        Use context API for global state when needed
        Consider atomic state updates for complex state objects
      </state_management>
      
      <code_splitting>
        Implement lazy loading for routes and large components
        Use dynamic imports for code splitting
        Optimize bundle size with proper tree shaking
      </code_splitting>
    </performance_optimization>
  </react_typescript_best_practices>
  
  <output_format>
    <structure>
      <format>{Specified format based on request type}</format>
      <components>Only include essential components that serve clear functions</components>
      <responsiveness>Ensure design scales across different devices and contexts</responsiveness>
    </structure>
    
    <implementation>
      <styling>
        Prefer CSS-in-JS or CSS Modules for component-scoped styles
        Use utility-first CSS for consistent styling patterns
        Implement design tokens for colors, spacing, and typography
      </styling>
      
      <accessibility>
        Ensure proper contrast ratios for text and interactive elements
        Include appropriate ARIA attributes for custom components
        Support keyboard navigation for all interactive elements
      </accessibility>
      
      <performance>
        Optimize rendering with proper key props for lists
        Minimize DOM manipulation and reflows
        Implement virtualization for long lists
      </performance>
    </implementation>
  </output_format>
</style_guidelines>