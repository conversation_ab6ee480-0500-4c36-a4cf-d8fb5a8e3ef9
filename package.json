{"name": "v3-admin-vite", "type": "module", "version": "5.0.0-beta.5", "description": "A crafted admin template, built with Vue3, Vite, TypeScript, Element Plus, and more", "author": "pany <<EMAIL>> (https://github.com/pany-ang)", "repository": "https://github.com/un-pany/v3-admin-vite", "scripts": {"dev": "vite", "build:staging": "vue-tsc && vite build --mode staging", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --fix", "prepare": "husky", "test": "vitest"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "axios": "1.8.4", "dayjs": "1.11.13", "element-plus": "2.9.7", "js-cookie": "3.0.5", "lodash-es": "4.17.21", "mitt": "3.0.1", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "8.2.0", "pinia": "3.0.1", "screenfull": "6.0.2", "vue": "3.5.13", "vue-router": "4.5.0", "vxe-table": "4.6.25"}, "devDependencies": {"@antfu/eslint-config": "4.11.0", "@types/js-cookie": "3.0.6", "@types/lodash-es": "4.17.12", "@types/node": "22.13.14", "@types/nprogress": "0.2.3", "@types/path-browserify": "1.0.3", "@vitejs/plugin-vue": "5.2.3", "@vitejs/plugin-vue-jsx": "4.1.2", "@vue/test-utils": "2.4.6", "eslint": "9.23.0", "eslint-plugin-format": "1.0.1", "happy-dom": "17.4.4", "husky": "9.1.7", "lint-staged": "15.5.0", "sass": "1.78.0", "typescript": "5.8.2", "unocss": "66.1.0-beta.7", "unplugin-auto-import": "19.1.2", "unplugin-svg-component": "0.12.1", "unplugin-vue-components": "28.4.1", "vite": "6.2.3", "vite-svg-loader": "5.1.0", "vitest": "3.0.9", "vue-tsc": "2.2.8"}, "lint-staged": {"*": "eslint --fix"}}