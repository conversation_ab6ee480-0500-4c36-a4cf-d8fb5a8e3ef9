services:
  koalawiki:
    image: crpi-j9ha7sxwhatgtvj4.cn-shenzhen.personal.cr.aliyuncs.com/koala-ai/koala-wiki
    networks:
      - koala_network
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - KOALAWIKI_REPOSITORIES=/repositories
      - TASK_MAX_SIZE_PER_USER=5 # 每个用户AI处理文档生成的最大数量
      - REPAIR_MERMAID=1 # 是否进行Mermaid修复，1修复，其余不修复
      - CHAT_MODEL=gpt-4.1 # 必须要支持function的模型
      - ANALYSIS_MODEL=gpt-4.1 # 分析模型，用于生成仓库目录结构，这个很重要，模型越强，生成的目录结构越好，为空则使用ChatModel
      # 分析模型建议使用GPT-4.1  , CHAT模型可以用其他模型生成文档,以节省 token 开销
      - CHAT_API_KEY= #您的APIkey
      - LANGUAGE= # 设置生成语言默认为“中文”, 英文可以填写 English 或 英文
      - ENDPOINT=https://api.token-ai.cn/v1
      - DB_TYPE=postgres
      - DB_CONNECTION_STRING=Host=postgres;Port=5432;Database=KoalaWiki;Username=postgres;Password=postgres
      - UPDATE_INTERVAL=5 # 仓库增量更新间隔，单位天
      - EnableSmartFilter=true # 是否启用智能过滤，这可能影响AI得到仓库的文件目录
      - ENABLE_INCREMENTAL_UPDATE=true # 是否启用增量更新
      - ENABLE_CODED_DEPENDENCY_ANALYSIS=false # 是否启用代码依赖分析？这可能会对代码的质量产生影响。
      - ENABLE_WAREHOUSE_FUNCTION_PROMPT_TASK=false # 是否启用MCP Prompt生成
      - ENABLE_WAREHOUSE_DESCRIPTION_TASK=false # 是否启用仓库Description生成
      - ENABLE_MEM0=true # 是否启用Mem0 RAG用于增强对话检索能力
      - MEM0_API_KEY=koala-ai # Mem0 API Key
      - MEM0_ENDPOINT=http://mem0:8000 # Mem0 API Endpoint
      - OTEL_SERVICE_NAME=koalawiki
      - OTEL_EXPORTER_OTLP_PROTOCOL=grpc
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://aspire-dashboard:18889
    volumes:
      - ./repositories:/app/repositories
      - ./data:/data

  mem0:
    container_name: mem0
    image: registry.cn-shenzhen.aliyuncs.com/tokengo/mem0
    build:
      context: ./server  # Set context to parent directory
      dockerfile: Dockerfile
    networks:
      - koala_network
    volumes:
      - ./history:/app/history      # History db location. By default, it creates a history.db file on the server folder
    depends_on:
      postgres:
        condition: service_healthy
      neo4j:
        condition: service_healthy
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload  # Enable auto-reload
    environment:
      - PYTHONDONTWRITEBYTECODE=1  # Prevents Python from writing .pyc files
      - PYTHONUNBUFFERED=1  # Ensures Python output is sent straight to terminal
      - API_KEY=koala-ai
      - OPENAI_BASE_URL=https://api.token-ai.cn/v1 # OpenAI API base URL, change if using a different provider
      - OPENAI_API_KEY= # Your OpenAI API key, if needed
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=mem0graph
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_COLLECTION_NAME=mem0Net # Mem0使用的Postgres集合名称
      - OPENAI_CHAT_MODEL=gpt-4.1-mini # 使用的Agent模型
      - EMBEDDING_MODEL_DIMS=1024 # 对应的嵌入模型维度，注意一旦指定维度则不能更改
      - OPENAI_EMBEDDING_MODEL=Qwen3-Embedding-0.6B # 使用的嵌入模型
  
  postgres:
    image: ankane/pgvector:v0.5.1
    restart: on-failure
    shm_size: "128mb" # Increase this if vacuuming fails with a "no space left on device" error
    networks:
      - koala_network
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    healthcheck:
      test: ["CMD", "pg_isready", "-q", "-d", "postgres", "-U", "postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - ./postgres_db:/var/lib/postgresql/data

  neo4j:
    image: neo4j:5.26.4
    container_name: neo4j
    networks:
      - koala_network
    healthcheck:
      test: wget http://localhost:7687 || exit 1
      interval: 1s
      timeout: 10s
      retries: 20
      start_period: 3s
    ports:
      - "7474:7474" # HTTP
      - "7687:7687" # Bolt
    volumes:
      - ./neo4j_data:/data
    environment:
      - NEO4J_AUTH=neo4j/mem0graph
      - NEO4J_PLUGINS=["apoc"]  # Add this line to install APOC
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
  
  aspire-dashboard:
    image: mcr.microsoft.com/dotnet/aspire-dashboard
    container_name: aspire-dashboard
    restart: always
    environment:
      - TZ=Asia/Shanghai
      - Dashboard:ApplicationName=Aspire
networks:
  koala_network:
    driver: bridge