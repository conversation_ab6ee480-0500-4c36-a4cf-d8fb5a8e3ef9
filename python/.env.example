# Database Configuration
DB_TYPE=sqlite  # sqlite, postgresql, mysql
DB_CONNECTION_STRING=sqlite:///./koala_wiki.db
# For PostgreSQL: postgresql+asyncpg://user:password@localhost/koala_wiki
# For MySQL: mysql+aiomysql://user:password@localhost/koala_wiki

# AI Configuration
CHAT_MODEL=gpt-4-turbo-preview
ANALYSIS_MODEL=gpt-4-turbo-preview
CHAT_API_KEY=your_openai_api_key_here
ENDPOINT=https://api.openai.com/v1
LANGUAGE=中文  # 中文 or English

# Repository Configuration
KOALAWIKI_REPOSITORIES=./repositories
UPDATE_INTERVAL=5  # days
ENABLE_INCREMENTAL_UPDATE=true
ENABLE_CODED_DEPENDENCY_ANALYSIS=false
ENABLE_SMART_FILTER=true

# Task Configuration
TASK_MAX_SIZE_PER_USER=5
REPAIR_MERMAID=1

# Security
SECRET_KEY=your_secret_key_here_change_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis Configuration (for background tasks)
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/koala_wiki.log

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false
RELOAD=false

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:3333"]

# Feature Flags
ENABLE_AGENT_TOOL=true
ENABLE_MCP=true
ENABLE_I18N=true
ENABLE_STATISTICS=true
ENABLE_MINIMAP=true

# File Upload
MAX_FILE_SIZE=100MB
ALLOWED_EXTENSIONS=[".md", ".txt", ".py", ".js", ".ts", ".java", ".go", ".cs", ".cpp", ".c", ".h"]

# Cache Configuration
CACHE_TTL=3600  # seconds
MEMORY_CACHE_SIZE=1000  # number of items
