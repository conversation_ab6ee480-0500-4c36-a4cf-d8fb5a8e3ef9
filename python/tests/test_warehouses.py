"""
Tests for warehouse endpoints.
"""

import pytest
from httpx import Async<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from koala_wiki.models.user import User
from koala_wiki.models.warehouse import Warehouse, WarehouseType, WarehouseStatus


class TestWarehouses:
    """Test warehouse endpoints."""
    
    @pytest.mark.asyncio
    async def test_create_git_warehouse(self, client: AsyncClient, auth_headers: dict):
        """Test creating a Git warehouse."""
        warehouse_data = {
            "address": "https://github.com/example/repo.git",
            "branch": "main",
            "description": "Test repository",
            "enable_ai_analysis": True,
            "enable_dependency_analysis": False
        }
        
        response = await client.post(
            "/api/v1/warehouses/git",
            json=warehouse_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["address"] == warehouse_data["address"]
        assert data["branch"] == warehouse_data["branch"]
        assert data["description"] == warehouse_data["description"]
        assert data["type"] == "git"
        assert data["status"] == "pending"
    
    @pytest.mark.asyncio
    async def test_create_file_warehouse(self, client: AsyncClient, auth_headers: dict):
        """Test creating a file warehouse."""
        warehouse_data = {
            "name": "Test File Warehouse",
            "file_path": "/tmp/test",
            "description": "Test file warehouse",
            "enable_ai_analysis": True
        }
        
        # This will fail because the path doesn't exist, but we can test the validation
        response = await client.post(
            "/api/v1/warehouses/file",
            json=warehouse_data,
            headers=auth_headers
        )
        
        assert response.status_code == 400
        assert "does not exist" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_get_user_warehouses(self, client: AsyncClient, auth_headers: dict):
        """Test getting user warehouses."""
        response = await client.get("/api/v1/warehouses/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    @pytest.mark.asyncio
    async def test_get_public_warehouses(self, client: AsyncClient):
        """Test getting public warehouses."""
        response = await client.get("/api/v1/warehouses/public")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    @pytest.mark.asyncio
    async def test_get_warehouse_unauthorized(self, client: AsyncClient):
        """Test getting warehouse without authentication."""
        response = await client.get("/api/v1/warehouses/")
        
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_warehouse(self, client: AsyncClient, auth_headers: dict):
        """Test getting nonexistent warehouse."""
        response = await client.get(
            "/api/v1/warehouses/nonexistent-id",
            headers=auth_headers
        )
        
        assert response.status_code == 404
        assert "Warehouse not found" in response.json()["detail"]
