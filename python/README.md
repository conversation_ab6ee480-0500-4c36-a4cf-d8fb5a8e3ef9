# KoalaWiki Python Implementation

A Python implementation of the KoalaWiki AI-driven code knowledge base system.

## Features

- 🤖 **AI-Powered Analysis**: Leverage OpenAI and LangChain for intelligent code analysis
- 📚 **Multi-Language Support**: Analyze repositories in Python, JavaScript, Java, Go, C#, and more
- 🔄 **Git Integration**: Seamless integration with Git repositories (GitHub, GitLab, Gitee)
- 📖 **Documentation Generation**: Automatic README and documentation generation
- 🗺️ **Knowledge Graphs**: Generate interactive mind maps and dependency graphs
- 🌐 **Multi-Database Support**: SQLite, PostgreSQL, MySQL support
- 🔐 **User Management**: Complete authentication and authorization system
- 🌍 **Internationalization**: Multi-language UI support
- 📊 **Analytics**: Comprehensive statistics and monitoring
- 🚀 **High Performance**: Async/await throughout, optimized for scale

## Quick Start

### Prerequisites

- Python 3.9+
- Git
- OpenAI API key (or compatible AI service)

### Installation

#### Option 1: Quick Start (Demo)

For a quick demo without full setup:

```bash
git clone https://github.com/AIDotNet/OpenDeepWiki.git
cd OpenDeepWiki/python
pip install fastapi uvicorn sqlalchemy aiosqlite python-dotenv loguru
python quick-start.py
```

#### Option 2: Automatic Installation (Recommended)

```bash
git clone https://github.com/AIDotNet/OpenDeepWiki.git
cd OpenDeepWiki/python
python install.py
```

The installation script will:
- Check Python version compatibility
- Create virtual environment
- Install dependencies
- Set up configuration files
- Initialize database

#### Option 3: Manual Installation

1. **Clone the repository**:
```bash
git clone https://github.com/AIDotNet/OpenDeepWiki.git
cd OpenDeepWiki/python
```

2. **Create virtual environment**:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**:
```bash
# For minimal installation
pip install -r requirements-minimal.txt

# Or for full features
pip install -r requirements.txt
```

4. **Set up environment variables**:
```bash
cp .env.example .env
# Edit .env with your configuration (especially CHAT_API_KEY)
```

5. **Initialize the database**:
```bash
python -m koala_wiki init-database
```

6. **Create an admin user**:
```bash
python -m koala_wiki create-admin
```

7. **Start the server**:
```bash
python -m koala_wiki serve
```

The API will be available at `http://localhost:8000` and documentation at `http://localhost:8000/docs`.

#### Troubleshooting

If you encounter dependency conflicts:

1. **Use minimal requirements**:
```bash
pip install -r requirements-minimal.txt
```

2. **Install specific packages individually**:
```bash
pip install fastapi uvicorn sqlalchemy aiosqlite openai python-dotenv
```

3. **Check Python version**:
```bash
python --version  # Should be 3.9+
```

4. **Test installation**:
```bash
python test-installation.py
```

## Configuration

Key configuration options in `.env`:

```env
# Database
DB_TYPE=sqlite  # sqlite, postgresql, mysql
DB_CONNECTION_STRING=sqlite:///./koala_wiki.db

# AI Configuration
CHAT_MODEL=gpt-4-turbo-preview
CHAT_API_KEY=your_openai_api_key_here
ENDPOINT=https://api.openai.com/v1

# Repository Configuration
KOALAWIKI_REPOSITORIES=./repositories
ENABLE_INCREMENTAL_UPDATE=true
ENABLE_CODED_DEPENDENCY_ANALYSIS=false

# Security
SECRET_KEY=your_secret_key_here_change_in_production
```

## API Usage

### Authentication

```python
import httpx

# Login
response = httpx.post("http://localhost:8000/api/v1/auth/login", data={
    "username": "admin",
    "password": "your_password"
})
token = response.json()["access_token"]

# Use token in subsequent requests
headers = {"Authorization": f"Bearer {token}"}
```

### Create a Warehouse

```python
# Add a Git repository
response = httpx.post(
    "http://localhost:8000/api/v1/warehouses/git",
    headers=headers,
    json={
        "address": "https://github.com/user/repo.git",
        "branch": "main",
        "description": "My awesome project"
    }
)
```

### Get Documents

```python
# List documents for a warehouse
warehouse_id = "your_warehouse_id"
response = httpx.get(
    f"http://localhost:8000/api/v1/documents/warehouse/{warehouse_id}",
    headers=headers
)
documents = response.json()
```

## Architecture

```
koala_wiki/
├── core/           # Core configuration and database
├── models/         # SQLAlchemy models
├── api/            # FastAPI routes
├── services/       # Business logic
├── utils/          # Utility functions
├── middleware/     # Custom middleware
├── ai/             # AI integration
├── git/            # Git operations
└── tasks/          # Background tasks
```

## Development

### Setup Development Environment

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest

# Format code
black src/
isort src/

# Type checking
mypy src/
```

### Database Migrations

```bash
# Create migration
alembic revision --autogenerate -m "Add new table"

# Apply migrations
alembic upgrade head
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=koala_wiki

# Run specific test file
pytest tests/test_auth.py
```

## Deployment

### Docker

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ ./src/
COPY alembic/ ./alembic/
COPY alembic.ini .

EXPOSE 8000
CMD ["python", "-m", "koala_wiki", "serve", "--host", "0.0.0.0"]
```

### Production Configuration

For production deployment:

1. Use PostgreSQL or MySQL instead of SQLite
2. Set up Redis for background tasks
3. Configure proper logging
4. Use a reverse proxy (nginx)
5. Set up SSL/TLS
6. Configure monitoring and alerting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- 📖 [Documentation](https://docs.opendeep.wiki)
- 🐛 [Issue Tracker](https://github.com/AIDotNet/OpenDeepWiki/issues)
- 💬 [Discussions](https://github.com/AIDotNet/OpenDeepWiki/discussions)

## Roadmap

- [ ] Enhanced code analysis with tree-sitter
- [ ] Real-time collaboration features
- [ ] Plugin system for custom analyzers
- [ ] Advanced visualization tools
- [ ] Integration with more AI models
- [ ] Mobile app support
