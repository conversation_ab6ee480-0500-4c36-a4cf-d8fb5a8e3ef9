#!/bin/bash

# Koala<PERSON>iki startup script

set -e

echo "Starting KoalaWiki..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "Creating .env file from template..."
    cp .env.example .env
    echo "Please edit .env file with your configuration"
fi

# Initialize database
echo "Initializing database..."
python -m koala_wiki init-database

# Check if admin user exists, if not create one
echo "Checking for admin user..."
if ! python -c "
import asyncio
from koala_wiki.core.database import get_db, init_db
from koala_wiki.services.auth import AuthService
from sqlalchemy import select
from koala_wiki.models.user import User

async def check_admin():
    await init_db()
    async for db in get_db():
        result = await db.execute(select(User).where(User.role == 'admin'))
        admin = result.scalar_one_or_none()
        if admin:
            print('Admin user exists')
            exit(0)
        else:
            print('No admin user found')
            exit(1)
        break

asyncio.run(check_admin())
"; then
    echo "Creating admin user..."
    python -m koala_wiki create-admin
fi

# Start the server
echo "Starting KoalaWiki server..."
python -m koala_wiki serve
