# Core web framework
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Database
sqlalchemy>=2.0.23
alembic>=1.13.1
asyncpg>=0.29.0  # PostgreSQL
aiomysql>=0.2.0  # MySQL
aiosqlite>=0.19.0  # SQLite

# AI and ML
openai>=1.3.7
langchain>=0.1.0
langchain-openai>=0.0.5
tiktoken>=0.5.2

# Git operations
GitPython>=3.1.40

# Code analysis (optional)
# tree-sitter>=0.21.0
# tree-sitter-python>=0.23.0

# Background tasks
celery>=5.3.4
redis>=5.0.1
rq>=1.15.1

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# HTTP client
httpx>=0.25.2
aiohttp>=3.9.1

# Utilities
python-dotenv>=1.0.0
click>=8.1.7
rich>=13.7.0
loguru>=0.7.2
pyyaml>=6.0.1
jinja2>=3.1.2
markdown>=3.5.1
beautifulsoup4>=4.12.2

# File processing
chardet>=5.2.0

# Monitoring and observability (optional)
# prometheus-client>=0.19.0
# opentelemetry-api>=1.21.0
# opentelemetry-sdk>=1.21.0

# Development
pytest>=7.4.3
pytest-asyncio>=0.21.1
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.1

# Optional dependencies for specific features
pygments>=2.17.2  # For syntax highlighting
