"""
Warehouse model for managing code repositories.
"""

import enum
from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Boolean, Column, DateTime, Enum, ForeignKey, 
    Integer, String, Text, Float
)
from sqlalchemy.orm import relationship

from .base import BaseModel, TimestampMixin, SoftDeleteMixin, MetadataMixin


class WarehouseStatus(enum.Enum):
    """Warehouse processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class WarehouseType(enum.Enum):
    """Warehouse type."""
    GIT = "git"
    FILE = "file"
    CUSTOM = "custom"


class Warehouse(BaseModel, TimestampMixin, SoftDeleteMixin, MetadataMixin):
    """Warehouse model for code repositories."""
    
    __tablename__ = "warehouses"
    
    # Basic information
    name = Column(String(255), nullable=False, index=True)
    organization_name = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    address = Column(String(500), nullable=False)  # Git URL or file path
    
    # Repository information
    branch = Column(String(100), default="main", nullable=False)
    version = Column(String(50), nullable=True)
    type = Column(Enum(WarehouseType), default=WarehouseType.GIT, nullable=False)
    
    # Processing status
    status = Column(Enum(WarehouseStatus), default=WarehouseStatus.PENDING, nullable=False)
    error = Column(Text, nullable=True)
    progress = Column(Float, default=0.0, nullable=False)  # 0.0 to 1.0
    
    # AI and processing configuration
    prompt = Column(Text, nullable=True)
    optimized_directory_structure = Column(Text, nullable=True)
    
    # Statistics
    stars = Column(Integer, default=0, nullable=False)
    forks = Column(Integer, default=0, nullable=False)
    size = Column(Integer, default=0, nullable=False)  # Repository size in bytes
    file_count = Column(Integer, default=0, nullable=False)
    line_count = Column(Integer, default=0, nullable=False)
    
    # Processing timestamps
    last_sync_at = Column(DateTime(timezone=True), nullable=True)
    last_analysis_at = Column(DateTime(timezone=True), nullable=True)
    processing_started_at = Column(DateTime(timezone=True), nullable=True)
    processing_completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Configuration flags
    enable_auto_sync = Column(Boolean, default=True, nullable=False)
    enable_ai_analysis = Column(Boolean, default=True, nullable=False)
    enable_dependency_analysis = Column(Boolean, default=False, nullable=False)
    
    # User relationship
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    user = relationship("User", back_populates="warehouses")
    
    # Document relationship
    documents = relationship("Document", back_populates="warehouse", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Warehouse(id={self.id}, name={self.name}, status={self.status.value})>"
    
    @property
    def is_processing(self) -> bool:
        """Check if warehouse is currently being processed."""
        return self.status == WarehouseStatus.PROCESSING
    
    @property
    def is_completed(self) -> bool:
        """Check if warehouse processing is completed."""
        return self.status == WarehouseStatus.COMPLETED
    
    @property
    def is_failed(self) -> bool:
        """Check if warehouse processing failed."""
        return self.status == WarehouseStatus.FAILED
    
    @property
    def processing_duration(self) -> Optional[float]:
        """Get processing duration in seconds."""
        if self.processing_started_at and self.processing_completed_at:
            delta = self.processing_completed_at - self.processing_started_at
            return delta.total_seconds()
        return None
    
    @property
    def git_url_without_extension(self) -> str:
        """Get git URL without .git extension."""
        return self.address.replace(".git", "") if self.address.endswith(".git") else self.address
    
    def start_processing(self):
        """Mark warehouse as processing."""
        self.status = WarehouseStatus.PROCESSING
        self.processing_started_at = datetime.utcnow()
        self.progress = 0.0
        self.error = None
    
    def complete_processing(self):
        """Mark warehouse as completed."""
        self.status = WarehouseStatus.COMPLETED
        self.processing_completed_at = datetime.utcnow()
        self.progress = 1.0
        self.last_analysis_at = datetime.utcnow()
    
    def fail_processing(self, error_message: str):
        """Mark warehouse as failed."""
        self.status = WarehouseStatus.FAILED
        self.processing_completed_at = datetime.utcnow()
        self.error = error_message
    
    def update_progress(self, progress: float):
        """Update processing progress."""
        self.progress = max(0.0, min(1.0, progress))
    
    def update_sync_timestamp(self):
        """Update last sync timestamp."""
        self.last_sync_at = datetime.utcnow()
    
    def update_statistics(self, stars: int = None, forks: int = None, 
                         size: int = None, file_count: int = None, 
                         line_count: int = None):
        """Update repository statistics."""
        if stars is not None:
            self.stars = stars
        if forks is not None:
            self.forks = forks
        if size is not None:
            self.size = size
        if file_count is not None:
            self.file_count = file_count
        if line_count is not None:
            self.line_count = line_count
    
    def to_dict(self, include_sensitive: bool = False) -> dict:
        """Convert warehouse to dictionary."""
        data = {
            "id": self.id,
            "name": self.name,
            "organization_name": self.organization_name,
            "description": self.description,
            "address": self.address,
            "branch": self.branch,
            "version": self.version,
            "type": self.type.value,
            "status": self.status.value,
            "progress": self.progress,
            "stars": self.stars,
            "forks": self.forks,
            "size": self.size,
            "file_count": self.file_count,
            "line_count": self.line_count,
            "last_sync_at": self.last_sync_at.isoformat() if self.last_sync_at else None,
            "last_analysis_at": self.last_analysis_at.isoformat() if self.last_analysis_at else None,
            "enable_auto_sync": self.enable_auto_sync,
            "enable_ai_analysis": self.enable_ai_analysis,
            "enable_dependency_analysis": self.enable_dependency_analysis,
            "user_id": self.user_id,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
        
        if include_sensitive:
            data.update({
                "error": self.error,
                "prompt": self.prompt,
                "optimized_directory_structure": self.optimized_directory_structure,
                "processing_started_at": self.processing_started_at.isoformat() if self.processing_started_at else None,
                "processing_completed_at": self.processing_completed_at.isoformat() if self.processing_completed_at else None,
                "processing_duration": self.processing_duration,
                "metadata": self.get_metadata(),
            })
        
        return data
