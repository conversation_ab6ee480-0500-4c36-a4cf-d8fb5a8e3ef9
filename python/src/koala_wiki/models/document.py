"""
Document models for storing generated documentation.
"""

import enum
from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Boolean, Column, DateTime, Enum, Foreign<PERSON>ey, 
    Integer, String, Text, Float
)
from sqlalchemy.orm import relationship

from .base import BaseModel, TimestampMixin, SoftDeleteMixin, MetadataMixin


class DocumentStatus(enum.Enum):
    """Document processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class DocumentType(enum.Enum):
    """Document type."""
    README = "readme"
    API_DOC = "api_doc"
    TUTORIAL = "tutorial"
    GUIDE = "guide"
    REFERENCE = "reference"
    CHANGELOG = "changelog"
    CUSTOM = "custom"


class Document(BaseModel, TimestampMixin, SoftDeleteMixin, MetadataMixin):
    """Document model for generated documentation."""
    
    __tablename__ = "documents"
    
    # Basic information
    title = Column(String(500), nullable=False)
    content = Column(Text, nullable=True)
    summary = Column(Text, nullable=True)
    
    # Document metadata
    type = Column(Enum(DocumentType), default=DocumentType.CUSTOM, nullable=False)
    language = Column(String(10), default="zh-CN", nullable=False)
    file_path = Column(String(1000), nullable=True)  # Original file path in repository
    git_path = Column(String(1000), nullable=False)  # Local git repository path
    
    # Processing status
    status = Column(Enum(DocumentStatus), default=DocumentStatus.PENDING, nullable=False)
    error = Column(Text, nullable=True)
    progress = Column(Float, default=0.0, nullable=False)
    
    # Content statistics
    word_count = Column(Integer, default=0, nullable=False)
    character_count = Column(Integer, default=0, nullable=False)
    reading_time = Column(Integer, default=0, nullable=False)  # Estimated reading time in minutes
    
    # Engagement metrics
    view_count = Column(Integer, default=0, nullable=False)
    like_count = Column(Integer, default=0, nullable=False)
    comment_count = Column(Integer, default=0, nullable=False)
    
    # AI generation metadata
    ai_model = Column(String(100), nullable=True)
    generation_prompt = Column(Text, nullable=True)
    generation_time = Column(Float, nullable=True)  # Time taken to generate in seconds
    
    # Versioning
    version = Column(String(50), default="1.0", nullable=False)
    parent_document_id = Column(String(36), ForeignKey("documents.id"), nullable=True)
    
    # Timestamps
    last_update = Column(DateTime(timezone=True), nullable=True)
    published_at = Column(DateTime(timezone=True), nullable=True)
    
    # Warehouse relationship
    warehouse_id = Column(String(36), ForeignKey("warehouses.id"), nullable=False, index=True)
    warehouse = relationship("Warehouse", back_populates="documents")
    
    # Self-referential relationship for versioning
    parent_document = relationship("Document", remote_side="Document.id", backref="child_documents")
    
    # Catalog relationship
    catalogs = relationship("DocumentCatalog", back_populates="document", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Document(id={self.id}, title={self.title}, status={self.status.value})>"
    
    @property
    def is_processing(self) -> bool:
        """Check if document is currently being processed."""
        return self.status == DocumentStatus.PROCESSING
    
    @property
    def is_completed(self) -> bool:
        """Check if document processing is completed."""
        return self.status == DocumentStatus.COMPLETED
    
    @property
    def is_failed(self) -> bool:
        """Check if document processing failed."""
        return self.status == DocumentStatus.FAILED
    
    @property
    def is_published(self) -> bool:
        """Check if document is published."""
        return self.published_at is not None
    
    def start_processing(self):
        """Mark document as processing."""
        self.status = DocumentStatus.PROCESSING
        self.progress = 0.0
        self.error = None
    
    def complete_processing(self):
        """Mark document as completed."""
        self.status = DocumentStatus.COMPLETED
        self.progress = 1.0
        self.last_update = datetime.utcnow()
    
    def fail_processing(self, error_message: str):
        """Mark document as failed."""
        self.status = DocumentStatus.FAILED
        self.error = error_message
    
    def update_progress(self, progress: float):
        """Update processing progress."""
        self.progress = max(0.0, min(1.0, progress))
    
    def publish(self):
        """Publish the document."""
        self.published_at = datetime.utcnow()
    
    def unpublish(self):
        """Unpublish the document."""
        self.published_at = None
    
    def increment_view_count(self):
        """Increment view count."""
        self.view_count += 1
    
    def increment_like_count(self):
        """Increment like count."""
        self.like_count += 1
    
    def decrement_like_count(self):
        """Decrement like count."""
        self.like_count = max(0, self.like_count - 1)
    
    def update_content_statistics(self):
        """Update content statistics based on content."""
        if self.content:
            self.character_count = len(self.content)
            self.word_count = len(self.content.split())
            # Estimate reading time (average 200 words per minute)
            self.reading_time = max(1, self.word_count // 200)
    
    def to_dict(self, include_content: bool = True, include_sensitive: bool = False) -> dict:
        """Convert document to dictionary."""
        data = {
            "id": self.id,
            "title": self.title,
            "summary": self.summary,
            "type": self.type.value,
            "language": self.language,
            "file_path": self.file_path,
            "status": self.status.value,
            "progress": self.progress,
            "word_count": self.word_count,
            "character_count": self.character_count,
            "reading_time": self.reading_time,
            "view_count": self.view_count,
            "like_count": self.like_count,
            "comment_count": self.comment_count,
            "version": self.version,
            "last_update": self.last_update.isoformat() if self.last_update else None,
            "published_at": self.published_at.isoformat() if self.published_at else None,
            "warehouse_id": self.warehouse_id,
            "parent_document_id": self.parent_document_id,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
        
        if include_content:
            data["content"] = self.content
        
        if include_sensitive:
            data.update({
                "git_path": self.git_path,
                "error": self.error,
                "ai_model": self.ai_model,
                "generation_prompt": self.generation_prompt,
                "generation_time": self.generation_time,
                "metadata": self.get_metadata(),
            })
        
        return data


class DocumentCatalog(BaseModel, TimestampMixin, SoftDeleteMixin):
    """Document catalog for organizing documents in a tree structure."""
    
    __tablename__ = "document_catalogs"
    
    # Basic information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    path = Column(String(1000), nullable=False)  # Full path in the catalog tree
    
    # Tree structure
    parent_id = Column(String(36), ForeignKey("document_catalogs.id"), nullable=True)
    level = Column(Integer, default=0, nullable=False)
    sort_order = Column(Integer, default=0, nullable=False)
    
    # Content
    is_folder = Column(Boolean, default=True, nullable=False)
    icon = Column(String(100), nullable=True)
    
    # Localization
    language = Column(String(10), default="zh-CN", nullable=False)
    
    # Document relationship
    document_id = Column(String(36), ForeignKey("documents.id"), nullable=True)
    document = relationship("Document", back_populates="catalogs")
    
    # Warehouse relationship
    warehouse_id = Column(String(36), ForeignKey("warehouses.id"), nullable=False, index=True)
    
    # Self-referential relationship for tree structure
    parent = relationship("DocumentCatalog", remote_side="DocumentCatalog.id", backref="children")
    
    def __repr__(self) -> str:
        return f"<DocumentCatalog(id={self.id}, name={self.name}, path={self.path})>"
    
    @property
    def is_root(self) -> bool:
        """Check if this is a root catalog."""
        return self.parent_id is None
    
    @property
    def has_children(self) -> bool:
        """Check if this catalog has children."""
        return len(self.children) > 0
    
    @property
    def has_document(self) -> bool:
        """Check if this catalog has an associated document."""
        return self.document_id is not None
    
    def get_full_path(self) -> str:
        """Get the full path from root to this catalog."""
        if self.is_root:
            return self.name
        
        path_parts = [self.name]
        current = self.parent
        while current:
            path_parts.insert(0, current.name)
            current = current.parent
        
        return "/".join(path_parts)
    
    def to_dict(self, include_children: bool = False, include_document: bool = False) -> dict:
        """Convert catalog to dictionary."""
        data = {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "path": self.path,
            "parent_id": self.parent_id,
            "level": self.level,
            "sort_order": self.sort_order,
            "is_folder": self.is_folder,
            "icon": self.icon,
            "language": self.language,
            "document_id": self.document_id,
            "warehouse_id": self.warehouse_id,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
        
        if include_children:
            data["children"] = [child.to_dict() for child in self.children]
        
        if include_document and self.document:
            data["document"] = self.document.to_dict(include_content=False)
        
        return data
