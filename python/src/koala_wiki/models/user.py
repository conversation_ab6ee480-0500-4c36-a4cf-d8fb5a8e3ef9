"""
User model and related functionality.
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Boolean, Column, DateTime, String, Text, Integer
from sqlalchemy.orm import relationship

from .base import BaseModel, TimestampMixin, SoftDeleteMixin


class User(BaseModel, TimestampMixin, SoftDeleteMixin):
    """User model."""
    
    __tablename__ = "users"
    
    # Basic information
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    
    # Profile information
    full_name = Column(String(100), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    
    # Status and permissions
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # Role-based access control
    role = Column(String(20), default="user", nullable=False)  # user, admin, moderator
    
    # Activity tracking
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    login_count = Column(Integer, default=0, nullable=False)
    
    # API access
    api_key = Column(String(255), nullable=True, unique=True)
    api_key_expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Preferences
    language = Column(String(10), default="zh-CN", nullable=False)
    timezone = Column(String(50), default="UTC", nullable=False)
    theme = Column(String(20), default="light", nullable=False)
    
    # Relationships
    warehouses = relationship("Warehouse", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, username={self.username}, email={self.email})>"
    
    @property
    def is_admin(self) -> bool:
        """Check if user is an admin."""
        return self.role == "admin" or self.is_superuser
    
    @property
    def is_moderator(self) -> bool:
        """Check if user is a moderator."""
        return self.role in ["admin", "moderator"] or self.is_superuser
    
    def update_last_login(self):
        """Update last login timestamp and increment login count."""
        self.last_login_at = datetime.utcnow()
        self.login_count += 1
    
    def can_create_warehouse(self, max_warehouses: int = 10) -> bool:
        """Check if user can create more warehouses."""
        if self.is_admin:
            return True
        return len(self.warehouses) < max_warehouses
    
    def to_dict(self, include_sensitive: bool = False) -> dict:
        """Convert user to dictionary."""
        data = {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "full_name": self.full_name,
            "avatar_url": self.avatar_url,
            "bio": self.bio,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "role": self.role,
            "last_login_at": self.last_login_at.isoformat() if self.last_login_at else None,
            "login_count": self.login_count,
            "language": self.language,
            "timezone": self.timezone,
            "theme": self.theme,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
        
        if include_sensitive:
            data.update({
                "is_superuser": self.is_superuser,
                "api_key": self.api_key,
                "api_key_expires_at": self.api_key_expires_at.isoformat() if self.api_key_expires_at else None,
            })
        
        return data
