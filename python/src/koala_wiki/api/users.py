"""
User API endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, EmailStr

from ..core.database import get_db
from ..models.user import User
from ..services.auth import AuthService

router = APIRouter()


class UserProfileUpdate(BaseModel):
    """User profile update schema."""
    full_name: Optional[str] = None
    bio: Optional[str] = None
    language: Optional[str] = None
    timezone: Optional[str] = None
    theme: Optional[str] = None


class UserResponse(BaseModel):
    """User response schema."""
    id: str
    username: str
    email: str
    full_name: Optional[str]
    bio: Optional[str]
    is_active: bool
    role: str
    language: str
    timezone: str
    theme: str
    created_at: str
    updated_at: str


@router.get("/me", response_model=UserResponse)
async def get_current_user_profile(
    current_user: User = Depends(AuthService.get_current_user)
):
    """Get current user profile."""
    return UserResponse(**current_user.to_dict())


@router.put("/me", response_model=UserResponse)
async def update_current_user_profile(
    profile_data: UserProfileUpdate,
    current_user: User = Depends(AuthService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update current user profile."""
    
    # Update allowed fields
    updates = profile_data.dict(exclude_none=True)
    for field, value in updates.items():
        if hasattr(current_user, field):
            setattr(current_user, field, value)
    
    await db.commit()
    await db.refresh(current_user)
    
    return UserResponse(**current_user.to_dict())


@router.get("/{user_id}", response_model=UserResponse)
async def get_user_profile(
    user_id: str,
    current_user: User = Depends(AuthService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user profile by ID."""
    
    user = await AuthService.get_user_by_id(user_id, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return UserResponse(**user.to_dict())
