"""
API routes for KoalaWiki.
"""

from fastapi import APIRouter

from .auth import router as auth_router
from .warehouses import router as warehouses_router
from .documents import router as documents_router
from .users import router as users_router
from .admin import router as admin_router

# Create main API router
api_router = APIRouter()

# Include sub-routers
api_router.include_router(auth_router, prefix="/auth", tags=["Authentication"])
api_router.include_router(warehouses_router, prefix="/warehouses", tags=["Warehouses"])
api_router.include_router(documents_router, prefix="/documents", tags=["Documents"])
api_router.include_router(users_router, prefix="/users", tags=["Users"])
api_router.include_router(admin_router, prefix="/admin", tags=["Administration"])

__all__ = ["api_router"]
