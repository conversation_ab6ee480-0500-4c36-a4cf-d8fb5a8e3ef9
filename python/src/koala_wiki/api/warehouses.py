"""
Warehouse API endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, HttpUrl

from ..core.database import get_db
from ..models.user import User
from ..models.warehouse import Warehouse, WarehouseStatus, WarehouseType
from ..services.auth import AuthService
from ..services.warehouse import WarehouseService

router = APIRouter()


class GitWarehouseCreate(BaseModel):
    """Schema for creating a Git warehouse."""
    address: HttpUrl
    branch: str = "main"
    description: str = ""
    enable_ai_analysis: bool = True
    enable_dependency_analysis: bool = False


class FileWarehouseCreate(BaseModel):
    """Schema for creating a file warehouse."""
    name: str
    file_path: str
    description: str = ""
    enable_ai_analysis: bool = True


class WarehouseUpdate(BaseModel):
    """Schema for updating a warehouse."""
    description: Optional[str] = None
    enable_ai_analysis: Optional[bool] = None
    enable_dependency_analysis: Optional[bool] = None
    enable_auto_sync: Optional[bool] = None
    prompt: Optional[str] = None


class WarehouseResponse(BaseModel):
    """Warehouse response schema."""
    id: str
    name: str
    organization_name: Optional[str]
    description: Optional[str]
    address: str
    branch: str
    type: str
    status: str
    progress: float
    stars: int
    forks: int
    size: int
    file_count: int
    line_count: int
    enable_ai_analysis: bool
    enable_dependency_analysis: bool
    enable_auto_sync: bool
    user_id: str
    created_at: str
    updated_at: str


@router.post("/git", response_model=WarehouseResponse)
async def create_git_warehouse(
    warehouse_data: GitWarehouseCreate,
    current_user: User = Depends(AuthService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new Git warehouse."""
    
    warehouse_service = WarehouseService(db)
    
    try:
        warehouse = await warehouse_service.create_git_warehouse(
            user=current_user,
            address=str(warehouse_data.address),
            branch=warehouse_data.branch,
            description=warehouse_data.description,
            enable_ai_analysis=warehouse_data.enable_ai_analysis,
            enable_dependency_analysis=warehouse_data.enable_dependency_analysis
        )
        
        return WarehouseResponse(**warehouse.to_dict())
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create warehouse: {str(e)}"
        )


@router.post("/file", response_model=WarehouseResponse)
async def create_file_warehouse(
    warehouse_data: FileWarehouseCreate,
    current_user: User = Depends(AuthService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new file warehouse."""
    
    warehouse_service = WarehouseService(db)
    
    try:
        warehouse = await warehouse_service.create_file_warehouse(
            user=current_user,
            name=warehouse_data.name,
            file_path=warehouse_data.file_path,
            description=warehouse_data.description,
            enable_ai_analysis=warehouse_data.enable_ai_analysis
        )
        
        return WarehouseResponse(**warehouse.to_dict())
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create warehouse: {str(e)}"
        )


@router.get("/", response_model=List[WarehouseResponse])
async def get_user_warehouses(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[WarehouseStatus] = None,
    search: Optional[str] = None,
    current_user: User = Depends(AuthService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's warehouses with pagination and filtering."""
    
    warehouse_service = WarehouseService(db)
    
    warehouses = await warehouse_service.get_user_warehouses(
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        status=status,
        search=search
    )
    
    return [WarehouseResponse(**warehouse.to_dict()) for warehouse in warehouses]


@router.get("/public", response_model=List[WarehouseResponse])
async def get_public_warehouses(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """Get public warehouses (completed ones) with pagination."""
    
    warehouse_service = WarehouseService(db)
    
    warehouses = await warehouse_service.get_public_warehouses(
        skip=skip,
        limit=limit,
        search=search
    )
    
    return [WarehouseResponse(**warehouse.to_dict()) for warehouse in warehouses]


@router.get("/{warehouse_id}", response_model=WarehouseResponse)
async def get_warehouse(
    warehouse_id: str,
    current_user: User = Depends(AuthService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get warehouse by ID."""
    
    warehouse_service = WarehouseService(db)
    warehouse = await warehouse_service.get_warehouse_by_id(warehouse_id)
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found"
        )
    
    # Check if user has access (owner or public completed warehouse)
    if warehouse.user_id != current_user.id and warehouse.status != WarehouseStatus.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return WarehouseResponse(**warehouse.to_dict())


@router.put("/{warehouse_id}", response_model=WarehouseResponse)
async def update_warehouse(
    warehouse_id: str,
    warehouse_data: WarehouseUpdate,
    current_user: User = Depends(AuthService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update warehouse."""
    
    warehouse_service = WarehouseService(db)
    
    # Convert Pydantic model to dict, excluding None values
    updates = warehouse_data.dict(exclude_none=True)
    
    warehouse = await warehouse_service.update_warehouse(
        warehouse_id=warehouse_id,
        user_id=current_user.id,
        **updates
    )
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found or access denied"
        )
    
    return WarehouseResponse(**warehouse.to_dict())


@router.delete("/{warehouse_id}")
async def delete_warehouse(
    warehouse_id: str,
    current_user: User = Depends(AuthService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete warehouse."""
    
    warehouse_service = WarehouseService(db)
    
    success = await warehouse_service.delete_warehouse(
        warehouse_id=warehouse_id,
        user_id=current_user.id
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found or access denied"
        )
    
    return {"message": "Warehouse deleted successfully"}


@router.post("/{warehouse_id}/sync")
async def sync_warehouse(
    warehouse_id: str,
    current_user: User = Depends(AuthService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Manually sync a warehouse."""
    
    warehouse_service = WarehouseService(db)
    warehouse = await warehouse_service.get_warehouse_by_id(warehouse_id)
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found"
        )
    
    if warehouse.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    if warehouse.type != WarehouseType.GIT:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only Git warehouses can be synced"
        )
    
    success = await warehouse_service.sync_warehouse(warehouse_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start sync"
        )
    
    return {"message": "Sync started successfully"}


@router.get("/{warehouse_id}/statistics")
async def get_warehouse_statistics(
    warehouse_id: str,
    current_user: User = Depends(AuthService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get warehouse statistics."""
    
    warehouse_service = WarehouseService(db)
    warehouse = await warehouse_service.get_warehouse_by_id(warehouse_id)
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found"
        )
    
    # Check if user has access
    if warehouse.user_id != current_user.id and warehouse.status != WarehouseStatus.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    statistics = await warehouse_service.get_warehouse_statistics(warehouse_id)
    return statistics
