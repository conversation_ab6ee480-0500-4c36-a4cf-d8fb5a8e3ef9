"""
Admin API endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from ..core.database import get_db
from ..models.user import User
from ..services.auth import AuthService

router = APIRouter()


class SystemStats(BaseModel):
    """System statistics schema."""
    total_users: int
    total_warehouses: int
    total_documents: int
    active_users: int
    processing_warehouses: int


@router.get("/stats", response_model=SystemStats)
async def get_system_statistics(
    current_user: User = Depends(AuthService.get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """Get system statistics (admin only)."""
    
    # TODO: Implement statistics collection
    return SystemStats(
        total_users=0,
        total_warehouses=0,
        total_documents=0,
        active_users=0,
        processing_warehouses=0
    )


@router.get("/users", response_model=List[dict])
async def list_all_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(AuthService.get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """List all users (admin only)."""
    
    # TODO: Implement user listing
    return []
