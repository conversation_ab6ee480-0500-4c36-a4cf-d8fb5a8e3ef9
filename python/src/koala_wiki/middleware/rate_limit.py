"""
Rate limiting middleware.
"""

import time
from typing import Callable, Dict
from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple in-memory rate limiting middleware."""
    
    def __init__(self, app, calls: int = 100, period: int = 60):
        """
        Initialize rate limiter.
        
        Args:
            app: FastAPI application
            calls: Number of calls allowed per period
            period: Time period in seconds
        """
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients: Dict[str, Dict] = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Apply rate limiting."""
        
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"
        
        # Skip rate limiting for certain paths
        skip_paths = ["/health", "/docs", "/redoc", "/openapi.json", "/static"]
        if any(request.url.path.startswith(path) for path in skip_paths):
            return await call_next(request)
        
        # Check rate limit
        current_time = time.time()
        
        if client_ip not in self.clients:
            self.clients[client_ip] = {
                "calls": 1,
                "reset_time": current_time + self.period
            }
        else:
            client_data = self.clients[client_ip]
            
            # Reset if period has passed
            if current_time > client_data["reset_time"]:
                client_data["calls"] = 1
                client_data["reset_time"] = current_time + self.period
            else:
                client_data["calls"] += 1
                
                # Check if limit exceeded
                if client_data["calls"] > self.calls:
                    raise HTTPException(
                        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                        detail="Rate limit exceeded",
                        headers={
                            "X-RateLimit-Limit": str(self.calls),
                            "X-RateLimit-Remaining": "0",
                            "X-RateLimit-Reset": str(int(client_data["reset_time"]))
                        }
                    )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        client_data = self.clients[client_ip]
        remaining = max(0, self.calls - client_data["calls"])
        
        response.headers["X-RateLimit-Limit"] = str(self.calls)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(client_data["reset_time"]))
        
        return response
