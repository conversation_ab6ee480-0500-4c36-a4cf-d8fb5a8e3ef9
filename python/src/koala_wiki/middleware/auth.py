"""
Authentication middleware.
"""

from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


class AuthMiddleware(BaseHTTPMiddleware):
    """Middleware for authentication processing."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process authentication for requests."""
        
        # Skip authentication for certain paths
        skip_paths = [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/health",
            "/",
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/static",
            "/uploads"
        ]
        
        # Check if path should skip authentication
        path = request.url.path
        should_skip = any(path.startswith(skip_path) for skip_path in skip_paths)
        
        if should_skip:
            return await call_next(request)
        
        # For now, just pass through - actual auth is handled by dependencies
        # This middleware could be used for additional auth processing
        
        response = await call_next(request)
        return response
