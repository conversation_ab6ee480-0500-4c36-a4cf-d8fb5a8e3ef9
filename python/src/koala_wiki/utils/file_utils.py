"""
File utility functions.
"""

import os
import mimetypes
from pathlib import Path
from typing import List, Optional

from ..core.config import settings


def ensure_directory(path: str) -> str:
    """Ensure directory exists and return absolute path."""
    abs_path = os.path.abspath(path)
    os.makedirs(abs_path, exist_ok=True)
    return abs_path


def get_file_size(file_path: str) -> int:
    """Get file size in bytes."""
    try:
        return os.path.getsize(file_path)
    except OSError:
        return 0


def get_file_extension(filename: str) -> str:
    """Get file extension from filename."""
    return Path(filename).suffix.lower()


def is_allowed_file(filename: str) -> bool:
    """Check if file extension is allowed."""
    extension = get_file_extension(filename)
    return extension in settings.allowed_extensions


def get_mime_type(filename: str) -> Optional[str]:
    """Get MIME type for file."""
    mime_type, _ = mimetypes.guess_type(filename)
    return mime_type


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage."""
    import re
    
    # Remove or replace unsafe characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    
    # Limit length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename


def get_relative_path(file_path: str, base_path: str) -> str:
    """Get relative path from base path."""
    try:
        return os.path.relpath(file_path, base_path)
    except ValueError:
        return file_path


def list_files_recursive(
    directory: str, 
    extensions: Optional[List[str]] = None,
    exclude_patterns: Optional[List[str]] = None
) -> List[str]:
    """List all files recursively in directory."""
    
    files = []
    exclude_patterns = exclude_patterns or []
    
    for root, dirs, filenames in os.walk(directory):
        # Skip hidden directories and common ignore patterns
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'venv']]
        
        for filename in filenames:
            # Skip hidden files
            if filename.startswith('.'):
                continue
            
            file_path = os.path.join(root, filename)
            
            # Check exclude patterns
            if any(pattern in file_path for pattern in exclude_patterns):
                continue
            
            # Check extensions
            if extensions:
                if get_file_extension(filename) not in extensions:
                    continue
            
            files.append(file_path)
    
    return files


def read_file_content(file_path: str, encoding: str = 'utf-8') -> str:
    """Read file content with encoding detection."""
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()
    except UnicodeDecodeError:
        # Try with different encodings
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        for enc in encodings:
            try:
                with open(file_path, 'r', encoding=enc) as f:
                    return f.read()
            except UnicodeDecodeError:
                continue
        
        # If all fail, read as binary and decode with errors='ignore'
        with open(file_path, 'rb') as f:
            return f.read().decode('utf-8', errors='ignore')


def write_file_content(file_path: str, content: str, encoding: str = 'utf-8') -> None:
    """Write content to file."""
    # Ensure directory exists
    directory = os.path.dirname(file_path)
    if directory:
        ensure_directory(directory)
    
    with open(file_path, 'w', encoding=encoding) as f:
        f.write(content)


def copy_file(src: str, dst: str) -> None:
    """Copy file from source to destination."""
    import shutil
    
    # Ensure destination directory exists
    dst_dir = os.path.dirname(dst)
    if dst_dir:
        ensure_directory(dst_dir)
    
    shutil.copy2(src, dst)


def move_file(src: str, dst: str) -> None:
    """Move file from source to destination."""
    import shutil
    
    # Ensure destination directory exists
    dst_dir = os.path.dirname(dst)
    if dst_dir:
        ensure_directory(dst_dir)
    
    shutil.move(src, dst)


def delete_file(file_path: str) -> bool:
    """Delete file if it exists."""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    except OSError:
        return False


def get_directory_size(directory: str) -> int:
    """Get total size of directory in bytes."""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(directory):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            try:
                total_size += os.path.getsize(file_path)
            except OSError:
                pass
    return total_size


def count_files_in_directory(directory: str) -> int:
    """Count total number of files in directory."""
    count = 0
    for root, dirs, files in os.walk(directory):
        count += len(files)
    return count
