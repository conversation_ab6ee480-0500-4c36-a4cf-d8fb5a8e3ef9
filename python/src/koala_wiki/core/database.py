"""
Database configuration and session management.
"""

import asyncio
from typing import As<PERSON><PERSON><PERSON>ator, Optional
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase, sessionmaker
from loguru import logger

from .config import settings


class Base(DeclarativeBase):
    """Base class for all database models."""
    metadata = MetaData()


class DatabaseManager:
    """Database manager for handling connections and sessions."""
    
    def __init__(self):
        self.engine = None
        self.async_engine = None
        self.session_factory = None
        self.async_session_factory = None
        self._initialized = False
    
    def initialize(self):
        """Initialize database connections."""
        if self._initialized:
            return
        
        database_url = settings.database_url
        
        # Create async engine
        if settings.is_sqlite:
            # For SQLite, use aiosqlite
            async_url = database_url.replace("sqlite://", "sqlite+aiosqlite://")
            self.async_engine = create_async_engine(
                async_url,
                echo=settings.debug,
                future=True,
                connect_args={"check_same_thread": False} if settings.is_sqlite else {}
            )
        elif settings.is_postgresql:
            # For PostgreSQL, use asyncpg
            if not database_url.startswith("postgresql+asyncpg://"):
                async_url = database_url.replace("postgresql://", "postgresql+asyncpg://")
            else:
                async_url = database_url
            self.async_engine = create_async_engine(
                async_url,
                echo=settings.debug,
                future=True,
                pool_size=20,
                max_overflow=0
            )
        elif settings.is_mysql:
            # For MySQL, use aiomysql
            if not database_url.startswith("mysql+aiomysql://"):
                async_url = database_url.replace("mysql://", "mysql+aiomysql://")
            else:
                async_url = database_url
            self.async_engine = create_async_engine(
                async_url,
                echo=settings.debug,
                future=True,
                pool_size=20,
                max_overflow=0
            )
        else:
            raise ValueError(f"Unsupported database type: {settings.db_type}")
        
        # Create session factories
        self.async_session_factory = async_sessionmaker(
            self.async_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # Create sync engine for migrations
        sync_url = database_url
        if settings.is_sqlite:
            sync_url = database_url
        elif settings.is_postgresql:
            sync_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
        elif settings.is_mysql:
            sync_url = database_url.replace("mysql+aiomysql://", "mysql://")
        
        self.engine = create_engine(sync_url, echo=settings.debug)
        self.session_factory = sessionmaker(self.engine)
        
        self._initialized = True
        logger.info(f"Database initialized with {settings.db_type}")
    
    async def create_tables(self):
        """Create all tables."""
        if not self._initialized:
            self.initialize()
        
        async with self.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database tables created")
    
    async def drop_tables(self):
        """Drop all tables."""
        if not self._initialized:
            self.initialize()
        
        async with self.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        
        logger.info("Database tables dropped")
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get an async database session."""
        if not self._initialized:
            self.initialize()
        
        async with self.async_session_factory() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    def get_sync_session(self):
        """Get a sync database session for migrations."""
        if not self._initialized:
            self.initialize()
        
        return self.session_factory()
    
    async def close(self):
        """Close database connections."""
        if self.async_engine:
            await self.async_engine.dispose()
        if self.engine:
            self.engine.dispose()
        
        logger.info("Database connections closed")


# Global database manager instance
db_manager = DatabaseManager()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get database session."""
    async for session in db_manager.get_session():
        yield session


async def init_db():
    """Initialize database and create tables."""
    db_manager.initialize()
    await db_manager.create_tables()


async def close_db():
    """Close database connections."""
    await db_manager.close()
