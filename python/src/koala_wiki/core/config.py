"""
Configuration management for KoalaWiki.
"""

import os
from typing import List, Optional, Union
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    app_name: str = "KoalaWiki"
    app_version: str = "0.9.0"
    debug: bool = False
    
    # Server
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False
    
    # Database
    db_type: str = "sqlite"
    db_connection_string: str = "sqlite:///./koala_wiki.db"
    
    # AI Configuration
    chat_model: str = "gpt-4-turbo-preview"
    analysis_model: str = "gpt-4-turbo-preview"
    chat_api_key: str = ""
    endpoint: str = "https://api.openai.com/v1"
    language: str = "中文"
    
    # Repository Configuration
    koalawiki_repositories: str = "./repositories"
    update_interval: int = 5  # days
    enable_incremental_update: bool = True
    enable_coded_dependency_analysis: bool = False
    enable_smart_filter: bool = True
    
    # Task Configuration
    task_max_size_per_user: int = 5
    repair_mermaid: int = 1
    
    # Security
    secret_key: str = "your_secret_key_here_change_in_production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Redis Configuration
    redis_url: str = "redis://localhost:6379/0"
    
    # Logging
    log_level: str = "INFO"
    log_file: str = "logs/koala_wiki.log"
    
    # CORS
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:3333"]
    
    # Feature Flags
    enable_agent_tool: bool = True
    enable_mcp: bool = True
    enable_i18n: bool = True
    enable_statistics: bool = True
    enable_minimap: bool = True
    
    # File Upload
    max_file_size: str = "100MB"
    allowed_extensions: List[str] = [
        ".md", ".txt", ".py", ".js", ".ts", ".java", 
        ".go", ".cs", ".cpp", ".c", ".h"
    ]
    
    # Cache
    cache_ttl: int = 3600  # seconds
    memory_cache_size: int = 1000
    
    @validator("cors_origins", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    @validator("allowed_extensions", pre=True)
    def assemble_allowed_extensions(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    @property
    def database_url(self) -> str:
        """Get the database URL based on the configuration."""
        return self.db_connection_string
    
    @property
    def is_sqlite(self) -> bool:
        """Check if using SQLite database."""
        return self.db_type.lower() == "sqlite"
    
    @property
    def is_postgresql(self) -> bool:
        """Check if using PostgreSQL database."""
        return self.db_type.lower() in ["postgresql", "postgres"]
    
    @property
    def is_mysql(self) -> bool:
        """Check if using MySQL database."""
        return self.db_type.lower() == "mysql"
    
    @property
    def repositories_path(self) -> str:
        """Get the absolute path to repositories directory."""
        return os.path.abspath(self.koalawiki_repositories)
    
    @property
    def max_file_size_bytes(self) -> int:
        """Convert max file size to bytes."""
        size_str = self.max_file_size.upper()
        if size_str.endswith("MB"):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith("KB"):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith("GB"):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()
