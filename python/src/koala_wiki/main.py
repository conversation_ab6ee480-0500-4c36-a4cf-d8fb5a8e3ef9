"""
Main FastAPI application factory.
"""

import os
from datetime import datetime
from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger

from .core.config import settings
from .core.database import init_db, close_db
from .api import api_router
from .middleware.logging import LoggingMiddleware
from .middleware.auth import AuthMiddleware
from .middleware.rate_limit import RateLimitMiddleware
from .utils.logging import setup_logging


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting KoalaWiki application...")

    # Setup logging
    setup_logging()

    # Initialize database
    await init_db()

    # Create repositories directory
    os.makedirs(settings.repositories_path, exist_ok=True)

    logger.info("KoalaWiki application started successfully")

    yield

    # Shutdown
    logger.info("Shutting down KoalaWiki application...")
    await close_db()
    logger.info("KoalaWiki application shut down successfully")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""

    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="AI-Driven Code Knowledge Base - Python Implementation",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        lifespan=lifespan
    )

    # Add middleware
    setup_middleware(app)

    # Add routes
    app.include_router(api_router, prefix="/api/v1")

    # Add health and root routes
    create_health_routes(app)

    # Add static files
    setup_static_files(app)

    # Add exception handlers
    setup_exception_handlers(app)

    return app


# Create the application instance
app = create_app()


def setup_middleware(app: FastAPI):
    """Setup application middleware."""

    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Gzip compression
    app.add_middleware(GZipMiddleware, minimum_size=1000)

    # Custom middleware
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(AuthMiddleware)
    app.add_middleware(RateLimitMiddleware)


def setup_static_files(app: FastAPI):
    """Setup static file serving."""

    # Serve static files if directory exists
    static_dir = "static"
    if os.path.exists(static_dir):
        app.mount("/static", StaticFiles(directory=static_dir), name="static")

    # Serve uploaded files
    uploads_dir = "uploads"
    os.makedirs(uploads_dir, exist_ok=True)
    app.mount("/uploads", StaticFiles(directory=uploads_dir), name="uploads")


def setup_exception_handlers(app: FastAPI):
    """Setup global exception handlers."""

    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """Global exception handler."""
        logger.error(f"Unhandled exception: {exc}", exc_info=True)

        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "message": "An unexpected error occurred",
                "detail": str(exc) if settings.debug else None
            }
        )

    @app.exception_handler(404)
    async def not_found_handler(request: Request, exc):
        """404 Not Found handler."""
        return JSONResponse(
            status_code=404,
            content={
                "error": "Not found",
                "message": "The requested resource was not found",
                "path": str(request.url.path)
            }
        )

    @app.exception_handler(422)
    async def validation_exception_handler(request: Request, exc):
        """Validation error handler."""
        return JSONResponse(
            status_code=422,
            content={
                "error": "Validation error",
                "message": "Request validation failed",
                "detail": exc.detail if hasattr(exc, 'detail') else str(exc)
            }
        )


def create_health_routes(app: FastAPI):
    """Create health and root routes."""

    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "version": settings.app_version,
            "timestamp": datetime.utcnow().isoformat()
        }

    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": "Welcome to KoalaWiki API",
            "version": settings.app_version,
            "docs": "/docs" if settings.debug else None
        }
