"""
Command line interface for KoalaWiki.
"""

import asyncio
import click
import uvicorn
from loguru import logger

from .core.config import settings
from .core.database import init_db, close_db, db_manager
from .main import create_app


@click.group()
def main():
    """KoalaWiki CLI - AI-Driven Code Knowledge Base."""
    pass


@main.command()
@click.option("--host", default=settings.host, help="Host to bind to")
@click.option("--port", default=settings.port, help="Port to bind to")
@click.option("--reload", is_flag=True, default=settings.reload, help="Enable auto-reload")
@click.option("--debug", is_flag=True, default=settings.debug, help="Enable debug mode")
def serve(host: str, port: int, reload: bool, debug: bool):
    """Start the KoalaWiki server."""
    
    # Update settings
    settings.host = host
    settings.port = port
    settings.reload = reload
    settings.debug = debug
    
    logger.info(f"Starting KoalaWiki server on {host}:{port}")
    
    uvicorn.run(
        "koala_wiki.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info" if not debug else "debug",
        access_log=True
    )


@main.command()
def init_database():
    """Initialize the database and create tables."""
    
    async def _init():
        logger.info("Initializing database...")
        await init_db()
        logger.info("Database initialized successfully")
        await close_db()
    
    asyncio.run(_init())


@main.command()
@click.confirmation_option(prompt="Are you sure you want to drop all tables?")
def drop_database():
    """Drop all database tables."""
    
    async def _drop():
        logger.info("Dropping database tables...")
        await db_manager.drop_tables()
        logger.info("Database tables dropped successfully")
        await close_db()
    
    asyncio.run(_drop())


@main.command()
@click.option("--username", prompt=True, help="Admin username")
@click.option("--email", prompt=True, help="Admin email")
@click.option("--password", prompt=True, hide_input=True, help="Admin password")
@click.option("--full-name", help="Admin full name")
def create_admin(username: str, email: str, password: str, full_name: str):
    """Create an admin user."""
    
    async def _create_admin():
        from .services.auth import AuthService
        from .core.database import get_db
        
        logger.info("Creating admin user...")
        
        # Initialize database
        await init_db()
        
        # Get database session
        async for db in get_db():
            try:
                user = await AuthService.create_user(
                    username=username,
                    email=email,
                    password=password,
                    full_name=full_name,
                    role="admin",
                    db=db
                )
                
                # Activate user
                await AuthService.activate_user(user, db)
                
                logger.info(f"Admin user '{username}' created successfully")
                break
                
            except Exception as e:
                logger.error(f"Failed to create admin user: {e}")
                break
        
        await close_db()
    
    asyncio.run(_create_admin())


@main.command()
def version():
    """Show KoalaWiki version."""
    from . import __version__
    click.echo(f"KoalaWiki version {__version__}")


@main.command()
def config():
    """Show current configuration."""
    
    click.echo("KoalaWiki Configuration:")
    click.echo(f"  App Name: {settings.app_name}")
    click.echo(f"  Version: {settings.app_version}")
    click.echo(f"  Debug: {settings.debug}")
    click.echo(f"  Host: {settings.host}")
    click.echo(f"  Port: {settings.port}")
    click.echo(f"  Database Type: {settings.db_type}")
    click.echo(f"  Database URL: {settings.database_url}")
    click.echo(f"  Repositories Path: {settings.repositories_path}")
    click.echo(f"  Log Level: {settings.log_level}")
    click.echo(f"  Log File: {settings.log_file}")


@main.command()
@click.option("--check-db", is_flag=True, help="Check database connection")
@click.option("--check-ai", is_flag=True, help="Check AI service connection")
def health(check_db: bool, check_ai: bool):
    """Check system health."""
    
    async def _health_check():
        logger.info("Running health checks...")
        
        if check_db:
            try:
                await init_db()
                logger.info("✓ Database connection: OK")
                await close_db()
            except Exception as e:
                logger.error(f"✗ Database connection: FAILED - {e}")
        
        if check_ai:
            try:
                from .services.ai import AIService
                # Test AI service connection
                # This would depend on your AI service implementation
                logger.info("✓ AI service connection: OK")
            except Exception as e:
                logger.error(f"✗ AI service connection: FAILED - {e}")
        
        logger.info("Health check completed")
    
    asyncio.run(_health_check())


@main.command()
@click.option("--format", type=click.Choice(["json", "table"]), default="table", help="Output format")
def list_users(format: str):
    """List all users."""
    
    async def _list_users():
        from .models.user import User
        from sqlalchemy import select
        import json
        
        await init_db()
        
        async for db in get_db():
            result = await db.execute(select(User))
            users = result.scalars().all()
            
            if format == "json":
                users_data = [user.to_dict() for user in users]
                click.echo(json.dumps(users_data, indent=2, default=str))
            else:
                click.echo(f"{'ID':<36} {'Username':<20} {'Email':<30} {'Role':<10} {'Active':<6}")
                click.echo("-" * 110)
                for user in users:
                    click.echo(f"{user.id:<36} {user.username:<20} {user.email:<30} {user.role:<10} {user.is_active}")
            
            break
        
        await close_db()
    
    asyncio.run(_list_users())


if __name__ == "__main__":
    main()
