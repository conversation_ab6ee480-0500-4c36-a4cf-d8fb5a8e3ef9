"""
AI service for code analysis and documentation generation.
"""

import os
import asyncio
from typing import List, Dict, Any, Optional
from openai import Async<PERSON><PERSON>A<PERSON>
from loguru import logger

from ..core.config import settings
from ..utils.file_utils import read_file_content, list_files_recursive


class AIService:
    """Service for AI-powered code analysis and documentation generation."""

    def __init__(self):
        self.client = AsyncOpenAI(
            api_key=settings.chat_api_key,
            base_url=settings.endpoint
        )
        self.chat_model = settings.chat_model
        self.analysis_model = settings.analysis_model
        self.language = settings.language

    async def generate_repository_documentation(
        self,
        repository_path: str,
        repository_url: str,
        branch: str = "main"
    ) -> List[Dict[str, Any]]:
        """Generate comprehensive documentation for a repository."""

        logger.info(f"Generating documentation for repository: {repository_path}")

        # Analyze repository structure
        structure = await self.analyze_repository_structure(repository_path)

        # Generate different types of documentation
        documentation = []

        # 1. Generate README
        readme = await self.generate_readme(repository_path, repository_url, branch, structure)
        if readme:
            documentation.append({
                "title": "README",
                "content": readme,
                "type": "readme",
                "language": self.language
            })

        # 2. Generate API documentation
        api_docs = await self.generate_api_documentation(repository_path, structure)
        if api_docs:
            documentation.append({
                "title": "API Documentation",
                "content": api_docs,
                "type": "api_doc",
                "language": self.language
            })

        # 3. Generate architecture overview
        architecture = await self.generate_architecture_overview(repository_path, structure)
        if architecture:
            documentation.append({
                "title": "Architecture Overview",
                "content": architecture,
                "type": "guide",
                "language": self.language
            })

        # 4. Generate getting started guide
        getting_started = await self.generate_getting_started_guide(repository_path, structure)
        if getting_started:
            documentation.append({
                "title": "Getting Started",
                "content": getting_started,
                "type": "tutorial",
                "language": self.language
            })

        logger.info(f"Generated {len(documentation)} documentation sections")
        return documentation

    async def analyze_repository_structure(self, repository_path: str) -> Dict[str, Any]:
        """Analyze repository structure and extract key information."""

        # Get file list with relevant extensions
        code_extensions = ['.py', '.js', '.ts', '.java', '.go', '.cs', '.cpp', '.c', '.h', '.php', '.rb']
        config_extensions = ['.json', '.yml', '.yaml', '.toml', '.ini', '.cfg']
        doc_extensions = ['.md', '.rst', '.txt']

        code_files = list_files_recursive(repository_path, code_extensions)
        config_files = list_files_recursive(repository_path, config_extensions)
        doc_files = list_files_recursive(repository_path, doc_extensions)

        # Analyze project type and framework
        project_info = self._detect_project_type(repository_path, code_files, config_files)

        # Get directory structure
        structure = self._get_directory_structure(repository_path)

        # Analyze key files
        key_files = self._analyze_key_files(repository_path, code_files[:20])  # Limit to first 20 files

        return {
            "path": repository_path,
            "project_info": project_info,
            "structure": structure,
            "code_files": code_files[:50],  # Limit for performance
            "config_files": config_files,
            "doc_files": doc_files,
            "key_files": key_files,
            "file_counts": {
                "code": len(code_files),
                "config": len(config_files),
                "docs": len(doc_files)
            }
        }

    async def generate_readme(
        self,
        repository_path: str,
        repository_url: str,
        branch: str,
        structure: Dict[str, Any]
    ) -> str:
        """Generate README.md content."""

        # Check if README already exists
        existing_readme = None
        for readme_file in ['README.md', 'README.rst', 'README.txt', 'readme.md']:
            readme_path = os.path.join(repository_path, readme_file)
            if os.path.exists(readme_path):
                existing_readme = read_file_content(readme_path)
                break

        # Prepare context for AI
        context = self._prepare_repository_context(structure)

        prompt = f"""
请为以下代码仓库生成一个详细的README.md文档。

仓库信息：
- URL: {repository_url}
- 分支: {branch}
- 项目类型: {structure['project_info'].get('type', 'Unknown')}
- 主要语言: {structure['project_info'].get('primary_language', 'Unknown')}
- 框架: {structure['project_info'].get('frameworks', [])}

目录结构：
{context['structure_summary']}

主要文件：
{context['key_files_summary']}

{'现有README内容：\n' + existing_readme + '\n' if existing_readme else ''}

请生成一个包含以下部分的README.md：
1. 项目标题和简介
2. 功能特性
3. 安装说明
4. 使用方法
5. 项目结构说明
6. 贡献指南
7. 许可证信息

请使用{self.language}编写，格式为Markdown。
"""

        try:
            response = await self.client.chat.completions.create(
                model=self.chat_model,
                messages=[
                    {"role": "system", "content": f"你是一个专业的技术文档编写专家，擅长为代码项目编写清晰、详细的README文档。请使用{self.language}回答。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=4000
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Failed to generate README: {e}")
            return ""

    async def generate_api_documentation(
        self,
        repository_path: str,
        structure: Dict[str, Any]
    ) -> str:
        """Generate API documentation."""

        # Find API-related files
        api_files = []
        for file_path in structure['code_files']:
            if any(keyword in file_path.lower() for keyword in ['api', 'controller', 'route', 'endpoint']):
                api_files.append(file_path)

        if not api_files:
            return ""

        # Analyze API files
        api_content = ""
        for file_path in api_files[:10]:  # Limit to 10 files
            try:
                content = read_file_content(file_path)
                api_content += f"\n\n=== {os.path.basename(file_path)} ===\n{content[:2000]}"
            except Exception:
                continue

        if not api_content:
            return ""

        prompt = f"""
请基于以下API相关代码文件，生成详细的API文档。

项目类型: {structure['project_info'].get('type', 'Unknown')}
主要语言: {structure['project_info'].get('primary_language', 'Unknown')}

API代码文件内容：
{api_content}

请生成包含以下内容的API文档：
1. API概述
2. 认证方式
3. 端点列表
4. 请求/响应格式
5. 错误代码说明
6. 使用示例

请使用{self.language}编写，格式为Markdown。
"""

        try:
            response = await self.client.chat.completions.create(
                model=self.analysis_model,
                messages=[
                    {"role": "system", "content": f"你是一个专业的API文档编写专家。请使用{self.language}回答。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.5,
                max_tokens=3000
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Failed to generate API documentation: {e}")
            return ""

    async def generate_architecture_overview(
        self,
        repository_path: str,
        structure: Dict[str, Any]
    ) -> str:
        """Generate architecture overview documentation."""

        context = self._prepare_repository_context(structure)

        prompt = f"""
请基于以下代码仓库信息，生成详细的架构概述文档。

项目信息：
- 类型: {structure['project_info'].get('type', 'Unknown')}
- 主要语言: {structure['project_info'].get('primary_language', 'Unknown')}
- 框架: {structure['project_info'].get('frameworks', [])}
- 依赖: {structure['project_info'].get('dependencies', [])}

目录结构：
{context['structure_summary']}

关键组件：
{context['key_files_summary']}

请生成包含以下内容的架构文档：
1. 系统架构概述
2. 核心组件说明
3. 数据流图
4. 技术栈介绍
5. 设计模式和原则
6. 扩展性考虑

请使用{self.language}编写，格式为Markdown。
"""

        try:
            response = await self.client.chat.completions.create(
                model=self.analysis_model,
                messages=[
                    {"role": "system", "content": f"你是一个资深的软件架构师，擅长分析和描述软件系统架构。请使用{self.language}回答。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.6,
                max_tokens=3000
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Failed to generate architecture overview: {e}")
            return ""

    async def generate_getting_started_guide(
        self,
        repository_path: str,
        structure: Dict[str, Any]
    ) -> str:
        """Generate getting started guide."""

        # Look for setup/installation files
        setup_files = []
        for file_path in structure['config_files']:
            if any(name in os.path.basename(file_path).lower() for name in ['package.json', 'requirements.txt', 'pom.xml', 'go.mod', 'cargo.toml']):
                setup_files.append(file_path)

        setup_content = ""
        for file_path in setup_files:
            try:
                content = read_file_content(file_path)
                setup_content += f"\n\n=== {os.path.basename(file_path)} ===\n{content[:1000]}"
            except Exception:
                continue

        prompt = f"""
请基于以下项目信息，生成详细的快速开始指南。

项目信息：
- 类型: {structure['project_info'].get('type', 'Unknown')}
- 主要语言: {structure['project_info'].get('primary_language', 'Unknown')}
- 框架: {structure['project_info'].get('frameworks', [])}

配置文件内容：
{setup_content}

请生成包含以下内容的快速开始指南：
1. 环境要求
2. 安装步骤
3. 配置说明
4. 运行方法
5. 基本使用示例
6. 常见问题解答

请使用{self.language}编写，格式为Markdown。
"""

        try:
            response = await self.client.chat.completions.create(
                model=self.chat_model,
                messages=[
                    {"role": "system", "content": f"你是一个专业的技术文档编写专家，擅长编写清晰易懂的快速开始指南。请使用{self.language}回答。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=3000
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Failed to generate getting started guide: {e}")
            return ""

    async def analyze_code_dependencies(self, repository_path: str) -> Dict[str, Any]:
        """Analyze code dependencies and relationships."""

        # This is a simplified implementation
        # In a real scenario, you'd use language-specific parsers

        dependencies = {
            "internal": [],
            "external": [],
            "imports": {},
            "exports": {},
            "call_graph": {}
        }

        # Analyze based on file extensions
        code_files = list_files_recursive(repository_path, ['.py', '.js', '.ts', '.java', '.go', '.cs'])

        for file_path in code_files[:50]:  # Limit for performance
            try:
                content = read_file_content(file_path)
                file_deps = self._extract_file_dependencies(content, file_path)

                relative_path = os.path.relpath(file_path, repository_path)
                dependencies["imports"][relative_path] = file_deps["imports"]
                dependencies["exports"][relative_path] = file_deps["exports"]

            except Exception as e:
                logger.warning(f"Failed to analyze dependencies for {file_path}: {e}")
                continue

        return dependencies

    async def generate_mind_map(
        self,
        repository_path: str,
        structure: Dict[str, Any]
    ) -> str:
        """Generate mind map for repository."""

        context = self._prepare_repository_context(structure)

        prompt = f"""
请基于以下代码仓库信息，生成Mermaid格式的思维导图。

项目信息：
- 类型: {structure['project_info'].get('type', 'Unknown')}
- 主要语言: {structure['project_info'].get('primary_language', 'Unknown')}

目录结构：
{context['structure_summary']}

请生成一个展示项目结构和组件关系的思维导图，使用Mermaid mindmap语法。
"""

        try:
            response = await self.client.chat.completions.create(
                model=self.analysis_model,
                messages=[
                    {"role": "system", "content": "你是一个专业的信息可视化专家，擅长创建清晰的思维导图。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.5,
                max_tokens=2000
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Failed to generate mind map: {e}")
            return ""

    def _detect_project_type(
        self,
        repository_path: str,
        code_files: List[str],
        config_files: List[str]
    ) -> Dict[str, Any]:
        """Detect project type and framework."""

        project_info = {
            "type": "Unknown",
            "primary_language": "Unknown",
            "frameworks": [],
            "dependencies": []
        }

        # Detect by file extensions
        extensions = {}
        for file_path in code_files:
            ext = os.path.splitext(file_path)[1]
            extensions[ext] = extensions.get(ext, 0) + 1

        # Determine primary language
        if extensions.get('.py', 0) > 0:
            project_info["primary_language"] = "Python"
        elif extensions.get('.js', 0) > 0 or extensions.get('.ts', 0) > 0:
            project_info["primary_language"] = "JavaScript/TypeScript"
        elif extensions.get('.java', 0) > 0:
            project_info["primary_language"] = "Java"
        elif extensions.get('.go', 0) > 0:
            project_info["primary_language"] = "Go"
        elif extensions.get('.cs', 0) > 0:
            project_info["primary_language"] = "C#"

        # Detect frameworks by config files
        for config_file in config_files:
            filename = os.path.basename(config_file).lower()

            if filename == 'package.json':
                project_info["type"] = "Node.js Project"
                # Could parse package.json to get dependencies
            elif filename == 'requirements.txt' or filename == 'pyproject.toml':
                project_info["type"] = "Python Project"
            elif filename == 'pom.xml':
                project_info["type"] = "Java Maven Project"
            elif filename == 'build.gradle':
                project_info["type"] = "Java Gradle Project"
            elif filename == 'go.mod':
                project_info["type"] = "Go Module"
            elif filename.endswith('.csproj'):
                project_info["type"] = "C# .NET Project"

        return project_info

    def _get_directory_structure(self, repository_path: str, max_depth: int = 3) -> Dict[str, Any]:
        """Get simplified directory structure."""

        def build_structure(path: str, current_depth: int = 0) -> Dict[str, Any]:
            if current_depth >= max_depth:
                return {"type": "directory", "truncated": True}

            if os.path.isfile(path):
                return {"type": "file"}

            structure = {"type": "directory", "children": {}}

            try:
                items = os.listdir(path)
                # Filter out hidden files and common ignore patterns
                items = [
                    item for item in items
                    if not item.startswith('.') and item not in ['node_modules', '__pycache__', 'venv', 'target', 'build']
                ]

                for item in sorted(items)[:20]:  # Limit to 20 items per directory
                    item_path = os.path.join(path, item)
                    structure["children"][item] = build_structure(item_path, current_depth + 1)

            except PermissionError:
                structure["error"] = "Permission denied"

            return structure

        return build_structure(repository_path)

    def _analyze_key_files(self, repository_path: str, code_files: List[str]) -> Dict[str, Any]:
        """Analyze key files to understand project structure."""

        key_files = {}

        for file_path in code_files:
            try:
                content = read_file_content(file_path)
                relative_path = os.path.relpath(file_path, repository_path)

                # Basic analysis
                lines = content.split('\n')
                key_files[relative_path] = {
                    "lines": len(lines),
                    "size": len(content),
                    "functions": self._count_functions(content, file_path),
                    "classes": self._count_classes(content, file_path),
                    "imports": self._extract_imports(content, file_path)
                }

            except Exception:
                continue

        return key_files

    def _count_functions(self, content: str, file_path: str) -> int:
        """Count functions in file based on language."""

        ext = os.path.splitext(file_path)[1]

        if ext == '.py':
            return content.count('def ')
        elif ext in ['.js', '.ts']:
            return content.count('function ') + content.count('=>')
        elif ext == '.java':
            return content.count('public ') + content.count('private ') + content.count('protected ')
        elif ext == '.go':
            return content.count('func ')
        elif ext == '.cs':
            return content.count('public ') + content.count('private ') + content.count('protected ')

        return 0

    def _count_classes(self, content: str, file_path: str) -> int:
        """Count classes in file based on language."""

        ext = os.path.splitext(file_path)[1]

        if ext == '.py':
            return content.count('class ')
        elif ext in ['.js', '.ts']:
            return content.count('class ')
        elif ext == '.java':
            return content.count('class ') + content.count('interface ')
        elif ext == '.cs':
            return content.count('class ') + content.count('interface ') + content.count('struct ')

        return 0

    def _extract_imports(self, content: str, file_path: str) -> List[str]:
        """Extract imports from file based on language."""

        imports = []
        ext = os.path.splitext(file_path)[1]
        lines = content.split('\n')

        for line in lines[:50]:  # Only check first 50 lines
            line = line.strip()

            if ext == '.py':
                if line.startswith('import ') or line.startswith('from '):
                    imports.append(line)
            elif ext in ['.js', '.ts']:
                if line.startswith('import ') or line.startswith('const ') and 'require(' in line:
                    imports.append(line)
            elif ext == '.java':
                if line.startswith('import '):
                    imports.append(line)
            elif ext == '.go':
                if line.startswith('import ') or (line.startswith('"') and line.endswith('"')):
                    imports.append(line)
            elif ext == '.cs':
                if line.startswith('using '):
                    imports.append(line)

        return imports[:10]  # Limit to 10 imports

    def _extract_file_dependencies(self, content: str, file_path: str) -> Dict[str, List[str]]:
        """Extract dependencies from a single file."""

        return {
            "imports": self._extract_imports(content, file_path),
            "exports": []  # Simplified - would need language-specific parsing
        }

    def _prepare_repository_context(self, structure: Dict[str, Any]) -> Dict[str, str]:
        """Prepare repository context for AI prompts."""

        # Create structure summary
        def format_structure(struct: Dict[str, Any], indent: int = 0) -> str:
            result = ""
            if struct.get("type") == "directory":
                children = struct.get("children", {})
                for name, child in list(children.items())[:10]:  # Limit items
                    result += "  " * indent + f"├── {name}\n"
                    if child.get("type") == "directory" and indent < 2:
                        result += format_structure(child, indent + 1)
            return result

        structure_summary = format_structure(structure["structure"])

        # Create key files summary
        key_files_summary = ""
        for file_path, info in list(structure["key_files"].items())[:10]:
            key_files_summary += f"- {file_path}: {info['lines']} lines, {info['functions']} functions, {info['classes']} classes\n"

        return {
            "structure_summary": structure_summary,
            "key_files_summary": key_files_summary
        }
