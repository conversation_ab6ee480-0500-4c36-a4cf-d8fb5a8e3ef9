"""
Authentication service for user management and JWT tokens.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JW<PERSON>rror, jwt
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..core.config import settings
from ..core.database import get_db
from ..models.user import User
from ..utils.security import verify_password

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")


class AuthService:
    """Authentication service."""
    
    @staticmethod
    async def authenticate_user(
        username: str, 
        password: str, 
        db: AsyncSession
    ) -> Optional[User]:
        """Authenticate user with username and password."""
        
        result = await db.execute(
            select(User).where(User.username == username)
        )
        user = result.scalar_one_or_none()
        
        if not user:
            return None
        
        if not verify_password(password, user.hashed_password):
            return None
        
        return user
    
    @staticmethod
    async def get_user_by_username(
        username: str, 
        db: AsyncSession
    ) -> Optional[User]:
        """Get user by username."""
        
        result = await db.execute(
            select(User).where(User.username == username)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_user_by_id(
        user_id: str, 
        db: AsyncSession
    ) -> Optional[User]:
        """Get user by ID."""
        
        result = await db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_user_by_email(
        email: str, 
        db: AsyncSession
    ) -> Optional[User]:
        """Get user by email."""
        
        result = await db.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_current_user(
        token: str = Depends(oauth2_scheme),
        db: AsyncSession = Depends(get_db)
    ) -> User:
        """Get current user from JWT token."""
        
        credentials_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
        try:
            payload = jwt.decode(
                token, 
                settings.secret_key, 
                algorithms=[settings.algorithm]
            )
            username: str = payload.get("sub")
            user_id: str = payload.get("user_id")
            
            if username is None or user_id is None:
                raise credentials_exception
                
        except JWTError:
            raise credentials_exception
        
        user = await AuthService.get_user_by_username(username, db)
        if user is None:
            raise credentials_exception
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )
        
        return user
    
    @staticmethod
    async def get_current_active_user(
        current_user: User = Depends(get_current_user)
    ) -> User:
        """Get current active user."""
        
        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )
        
        return current_user
    
    @staticmethod
    async def get_current_admin_user(
        current_user: User = Depends(get_current_user)
    ) -> User:
        """Get current admin user."""
        
        if not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
        
        return current_user
    
    @staticmethod
    async def get_current_moderator_user(
        current_user: User = Depends(get_current_user)
    ) -> User:
        """Get current moderator user."""
        
        if not current_user.is_moderator:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
        
        return current_user
    
    @staticmethod
    async def create_user(
        username: str,
        email: str,
        password: str,
        full_name: Optional[str] = None,
        role: str = "user",
        db: AsyncSession = None
    ) -> User:
        """Create a new user."""
        
        from ..utils.security import get_password_hash
        
        # Check if username exists
        existing_user = await AuthService.get_user_by_username(username, db)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )
        
        # Check if email exists
        existing_email = await AuthService.get_user_by_email(email, db)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Create user
        hashed_password = get_password_hash(password)
        user = User(
            username=username,
            email=email,
            hashed_password=hashed_password,
            full_name=full_name,
            role=role,
            is_active=True,
            is_verified=False
        )
        
        db.add(user)
        await db.commit()
        await db.refresh(user)
        
        return user
    
    @staticmethod
    async def update_user_password(
        user: User,
        new_password: str,
        db: AsyncSession
    ) -> User:
        """Update user password."""
        
        from ..utils.security import get_password_hash
        
        user.hashed_password = get_password_hash(new_password)
        await db.commit()
        await db.refresh(user)
        
        return user
    
    @staticmethod
    async def activate_user(
        user: User,
        db: AsyncSession
    ) -> User:
        """Activate user account."""
        
        user.is_active = True
        user.is_verified = True
        await db.commit()
        await db.refresh(user)
        
        return user
    
    @staticmethod
    async def deactivate_user(
        user: User,
        db: AsyncSession
    ) -> User:
        """Deactivate user account."""
        
        user.is_active = False
        await db.commit()
        await db.refresh(user)
        
        return user
