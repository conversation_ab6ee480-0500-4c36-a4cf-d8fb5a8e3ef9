"""
Git service for repository operations.
"""

import os
import re
import asyncio
from typing import Dict, Any, Optional
from urllib.parse import urlparse
from git import Repo, GitCommandError
from loguru import logger

from ..core.config import settings
from ..utils.file_utils import ensure_directory


class GitService:
    """Service for Git repository operations."""
    
    def __init__(self):
        self.repositories_path = ensure_directory(settings.repositories_path)
    
    def parse_repository_url(self, url: str) -> Dict[str, str]:
        """Parse repository URL to extract organization and name."""
        
        # Handle different URL formats
        patterns = [
            r'https?://github\.com/([^/]+)/([^/]+?)(?:\.git)?/?$',
            r'https?://gitlab\.com/([^/]+)/([^/]+?)(?:\.git)?/?$',
            r'https?://gitee\.com/([^/]+)/([^/]+?)(?:\.git)?/?$',
            r'git@github\.com:([^/]+)/([^/]+?)(?:\.git)?$',
            r'git@gitlab\.com:([^/]+)/([^/]+?)(?:\.git)?$',
            r'git@gitee\.com:([^/]+)/([^/]+?)(?:\.git)?$',
        ]
        
        for pattern in patterns:
            match = re.match(pattern, url)
            if match:
                organization, name = match.groups()
                return {
                    "organization": organization,
                    "name": name,
                    "full_name": f"{organization}/{name}"
                }
        
        # Fallback for unknown formats
        parsed = urlparse(url)
        path_parts = parsed.path.strip('/').split('/')
        
        if len(path_parts) >= 2:
            organization = path_parts[0]
            name = path_parts[1].replace('.git', '')
            return {
                "organization": organization,
                "name": name,
                "full_name": f"{organization}/{name}"
            }
        
        return {
            "organization": "unknown",
            "name": os.path.basename(url).replace('.git', ''),
            "full_name": os.path.basename(url).replace('.git', '')
        }
    
    def get_local_path(self, repository_url: str, branch: str = "main") -> str:
        """Get local path for repository."""
        
        repo_info = self.parse_repository_url(repository_url)
        safe_name = re.sub(r'[^\w\-_.]', '_', repo_info["full_name"])
        
        return os.path.join(
            self.repositories_path,
            safe_name,
            branch
        )
    
    async def clone_or_update_repository(
        self, 
        repository_url: str, 
        branch: str = "main"
    ) -> str:
        """Clone repository or update if it already exists."""
        
        local_path = self.get_local_path(repository_url, branch)
        
        if os.path.exists(local_path):
            logger.info(f"Updating existing repository: {repository_url}")
            return await self.update_repository(repository_url, branch)
        else:
            logger.info(f"Cloning repository: {repository_url}")
            return await self.clone_repository(repository_url, branch)
    
    async def clone_repository(
        self, 
        repository_url: str, 
        branch: str = "main"
    ) -> str:
        """Clone a Git repository."""
        
        local_path = self.get_local_path(repository_url, branch)
        ensure_directory(os.path.dirname(local_path))
        
        try:
            # Run git clone in a separate thread to avoid blocking
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._clone_repo_sync,
                repository_url,
                local_path,
                branch
            )
            
            logger.info(f"Successfully cloned repository to: {local_path}")
            return local_path
            
        except Exception as e:
            logger.error(f"Failed to clone repository {repository_url}: {e}")
            raise
    
    def _clone_repo_sync(self, repository_url: str, local_path: str, branch: str):
        """Synchronous repository cloning."""
        
        try:
            repo = Repo.clone_from(
                repository_url,
                local_path,
                branch=branch,
                depth=1  # Shallow clone for faster download
            )
            return repo
        except GitCommandError as e:
            # Try without specifying branch if it fails
            if "does not exist" in str(e) or "not found" in str(e):
                logger.warning(f"Branch '{branch}' not found, trying default branch")
                repo = Repo.clone_from(
                    repository_url,
                    local_path,
                    depth=1
                )
                return repo
            raise
    
    async def update_repository(
        self, 
        repository_url: str, 
        branch: str = "main"
    ) -> str:
        """Update an existing repository."""
        
        local_path = self.get_local_path(repository_url, branch)
        
        if not os.path.exists(local_path):
            return await self.clone_repository(repository_url, branch)
        
        try:
            # Run git pull in a separate thread
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._update_repo_sync,
                local_path,
                branch
            )
            
            logger.info(f"Successfully updated repository: {local_path}")
            return local_path
            
        except Exception as e:
            logger.error(f"Failed to update repository {repository_url}: {e}")
            # If update fails, try to re-clone
            import shutil
            shutil.rmtree(local_path, ignore_errors=True)
            return await self.clone_repository(repository_url, branch)
    
    def _update_repo_sync(self, local_path: str, branch: str):
        """Synchronous repository update."""
        
        repo = Repo(local_path)
        
        # Fetch latest changes
        origin = repo.remotes.origin
        origin.fetch()
        
        # Checkout the specified branch
        try:
            repo.git.checkout(branch)
        except GitCommandError:
            # If branch doesn't exist locally, create it
            repo.git.checkout('-b', branch, f'origin/{branch}')
        
        # Pull latest changes
        origin.pull()
        
        return repo
    
    async def get_repository_info(self, local_path: str) -> Dict[str, Any]:
        """Get repository information."""
        
        if not os.path.exists(local_path):
            return {}
        
        try:
            repo = Repo(local_path)
            
            # Get basic repository info
            info = {
                "path": local_path,
                "is_bare": repo.bare,
                "is_dirty": repo.is_dirty(),
                "active_branch": repo.active_branch.name if repo.active_branch else None,
                "head_commit": str(repo.head.commit) if repo.head.is_valid() else None,
                "commit_count": len(list(repo.iter_commits())),
                "branches": [branch.name for branch in repo.branches],
                "remotes": [remote.name for remote in repo.remotes],
            }
            
            # Get latest commit info
            if repo.head.is_valid():
                latest_commit = repo.head.commit
                info.update({
                    "latest_commit": {
                        "sha": str(latest_commit),
                        "message": latest_commit.message.strip(),
                        "author": str(latest_commit.author),
                        "date": latest_commit.committed_datetime.isoformat(),
                    }
                })
            
            # Get remote URL
            if repo.remotes:
                origin = repo.remotes.origin
                info["remote_url"] = list(origin.urls)[0] if origin.urls else None
            
            # Try to get GitHub/GitLab stats (this would require API calls)
            # For now, we'll set default values
            info.update({
                "stars": 0,
                "forks": 0,
                "watchers": 0,
                "issues": 0,
                "pull_requests": 0,
            })
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get repository info for {local_path}: {e}")
            return {}
    
    async def get_file_history(
        self, 
        local_path: str, 
        file_path: str, 
        max_count: int = 10
    ) -> list:
        """Get commit history for a specific file."""
        
        try:
            repo = Repo(local_path)
            commits = list(repo.iter_commits(paths=file_path, max_count=max_count))
            
            history = []
            for commit in commits:
                history.append({
                    "sha": str(commit),
                    "message": commit.message.strip(),
                    "author": str(commit.author),
                    "date": commit.committed_datetime.isoformat(),
                    "stats": commit.stats.files.get(file_path, {})
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Failed to get file history for {file_path}: {e}")
            return []
    
    async def get_repository_structure(self, local_path: str) -> Dict[str, Any]:
        """Get repository directory structure."""
        
        if not os.path.exists(local_path):
            return {}
        
        def build_tree(path: str, max_depth: int = 3, current_depth: int = 0) -> Dict[str, Any]:
            """Build directory tree structure."""
            
            if current_depth >= max_depth:
                return {"type": "directory", "truncated": True}
            
            if os.path.isfile(path):
                return {
                    "type": "file",
                    "size": os.path.getsize(path),
                    "extension": os.path.splitext(path)[1]
                }
            
            tree = {"type": "directory", "children": {}}
            
            try:
                items = os.listdir(path)
                # Skip hidden files and common ignore patterns
                items = [
                    item for item in items 
                    if not item.startswith('.') and item not in ['node_modules', '__pycache__', 'venv']
                ]
                
                for item in sorted(items):
                    item_path = os.path.join(path, item)
                    tree["children"][item] = build_tree(item_path, max_depth, current_depth + 1)
                    
            except PermissionError:
                tree["error"] = "Permission denied"
            
            return tree
        
        return build_tree(local_path)
    
    async def search_in_repository(
        self, 
        local_path: str, 
        query: str, 
        file_extensions: Optional[list] = None
    ) -> list:
        """Search for text in repository files."""
        
        if not os.path.exists(local_path):
            return []
        
        results = []
        file_extensions = file_extensions or ['.py', '.js', '.ts', '.java', '.go', '.cs', '.md']
        
        for root, dirs, files in os.walk(local_path):
            # Skip hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                if file.startswith('.'):
                    continue
                
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1]
                
                if file_ext not in file_extensions:
                    continue
                
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        for line_num, line in enumerate(f, 1):
                            if query.lower() in line.lower():
                                relative_path = os.path.relpath(file_path, local_path)
                                results.append({
                                    "file": relative_path,
                                    "line": line_num,
                                    "content": line.strip(),
                                    "match": query
                                })
                                
                                # Limit results per file
                                if len([r for r in results if r["file"] == relative_path]) >= 5:
                                    break
                
                except Exception:
                    continue
                
                # Limit total results
                if len(results) >= 100:
                    break
        
        return results
    
    def is_git_repository(self, path: str) -> bool:
        """Check if path is a Git repository."""
        try:
            Repo(path)
            return True
        except Exception:
            return False
    
    def get_repository_languages(self, local_path: str) -> Dict[str, int]:
        """Get programming languages used in repository."""
        
        language_extensions = {
            'Python': ['.py', '.pyw'],
            'JavaScript': ['.js', '.jsx'],
            'TypeScript': ['.ts', '.tsx'],
            'Java': ['.java'],
            'C#': ['.cs'],
            'Go': ['.go'],
            'C++': ['.cpp', '.cc', '.cxx'],
            'C': ['.c'],
            'PHP': ['.php'],
            'Ruby': ['.rb'],
            'Swift': ['.swift'],
            'Kotlin': ['.kt'],
            'Rust': ['.rs'],
            'HTML': ['.html', '.htm'],
            'CSS': ['.css'],
            'SCSS': ['.scss'],
            'Vue': ['.vue'],
            'Markdown': ['.md', '.markdown'],
            'JSON': ['.json'],
            'YAML': ['.yml', '.yaml'],
            'XML': ['.xml'],
            'SQL': ['.sql'],
            'Shell': ['.sh', '.bash'],
        }
        
        language_counts = {}
        
        for root, dirs, files in os.walk(local_path):
            # Skip hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                if file.startswith('.'):
                    continue
                
                file_ext = os.path.splitext(file)[1].lower()
                
                for language, extensions in language_extensions.items():
                    if file_ext in extensions:
                        language_counts[language] = language_counts.get(language, 0) + 1
                        break
        
        return language_counts
