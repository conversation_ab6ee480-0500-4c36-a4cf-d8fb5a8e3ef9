"""
Warehouse service for managing code repositories.
"""

import os
import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload
from loguru import logger

from ..models.warehouse import Warehouse, WarehouseStatus, WarehouseType
from ..models.user import User
from ..core.config import settings
from ..utils.file_utils import ensure_directory, get_directory_size, count_files_in_directory
from .git import GitService
from .ai import AIService


class WarehouseService:
    """Service for managing warehouses (code repositories)."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.git_service = GitService()
        self.ai_service = AIService()
    
    async def create_git_warehouse(
        self,
        user: User,
        address: str,
        branch: str = "main",
        description: str = "",
        enable_ai_analysis: bool = True,
        enable_dependency_analysis: bool = False
    ) -> Warehouse:
        """Create a new Git warehouse."""
        
        # Parse repository information from address
        repo_info = self.git_service.parse_repository_url(address)
        
        # Check if warehouse already exists for this user
        existing = await self.get_warehouse_by_address(user.id, address)
        if existing:
            raise ValueError(f"Warehouse with address '{address}' already exists")
        
        # Create warehouse
        warehouse = Warehouse(
            name=repo_info.get("name", "Unknown"),
            organization_name=repo_info.get("organization", ""),
            description=description,
            address=address,
            branch=branch,
            type=WarehouseType.GIT,
            status=WarehouseStatus.PENDING,
            enable_ai_analysis=enable_ai_analysis,
            enable_dependency_analysis=enable_dependency_analysis,
            user_id=user.id
        )
        
        self.db.add(warehouse)
        await self.db.commit()
        await self.db.refresh(warehouse)
        
        logger.info(f"Created Git warehouse: {warehouse.name} ({warehouse.id})")
        
        # Start processing in background
        asyncio.create_task(self._process_warehouse_async(warehouse.id))
        
        return warehouse
    
    async def create_file_warehouse(
        self,
        user: User,
        name: str,
        file_path: str,
        description: str = "",
        enable_ai_analysis: bool = True
    ) -> Warehouse:
        """Create a new file-based warehouse."""
        
        # Validate file path
        if not os.path.exists(file_path):
            raise ValueError(f"File path '{file_path}' does not exist")
        
        # Check if warehouse already exists for this user
        existing = await self.get_warehouse_by_address(user.id, file_path)
        if existing:
            raise ValueError(f"Warehouse with path '{file_path}' already exists")
        
        # Create warehouse
        warehouse = Warehouse(
            name=name,
            organization_name="",
            description=description,
            address=file_path,
            branch="",
            type=WarehouseType.FILE,
            status=WarehouseStatus.PENDING,
            enable_ai_analysis=enable_ai_analysis,
            enable_dependency_analysis=False,
            user_id=user.id
        )
        
        self.db.add(warehouse)
        await self.db.commit()
        await self.db.refresh(warehouse)
        
        logger.info(f"Created file warehouse: {warehouse.name} ({warehouse.id})")
        
        # Start processing in background
        asyncio.create_task(self._process_warehouse_async(warehouse.id))
        
        return warehouse
    
    async def get_warehouse_by_id(self, warehouse_id: str) -> Optional[Warehouse]:
        """Get warehouse by ID."""
        result = await self.db.execute(
            select(Warehouse)
            .options(selectinload(Warehouse.user))
            .where(Warehouse.id == warehouse_id)
        )
        return result.scalar_one_or_none()
    
    async def get_warehouse_by_address(self, user_id: str, address: str) -> Optional[Warehouse]:
        """Get warehouse by user ID and address."""
        result = await self.db.execute(
            select(Warehouse).where(
                and_(
                    Warehouse.user_id == user_id,
                    Warehouse.address == address
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def get_user_warehouses(
        self,
        user_id: str,
        skip: int = 0,
        limit: int = 100,
        status: Optional[WarehouseStatus] = None,
        search: Optional[str] = None
    ) -> List[Warehouse]:
        """Get warehouses for a user with pagination and filtering."""
        
        query = select(Warehouse).where(Warehouse.user_id == user_id)
        
        if status:
            query = query.where(Warehouse.status == status)
        
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    Warehouse.name.ilike(search_term),
                    Warehouse.description.ilike(search_term),
                    Warehouse.address.ilike(search_term)
                )
            )
        
        query = query.order_by(Warehouse.created_at.desc()).offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_public_warehouses(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None
    ) -> List[Warehouse]:
        """Get public warehouses (completed ones) with pagination."""
        
        query = select(Warehouse).where(
            Warehouse.status == WarehouseStatus.COMPLETED
        )
        
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    Warehouse.name.ilike(search_term),
                    Warehouse.description.ilike(search_term),
                    Warehouse.organization_name.ilike(search_term)
                )
            )
        
        query = query.order_by(Warehouse.stars.desc(), Warehouse.created_at.desc())
        query = query.offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def update_warehouse(
        self,
        warehouse_id: str,
        user_id: str,
        **updates
    ) -> Optional[Warehouse]:
        """Update warehouse information."""
        
        warehouse = await self.get_warehouse_by_id(warehouse_id)
        if not warehouse or warehouse.user_id != user_id:
            return None
        
        # Update allowed fields
        allowed_fields = [
            'description', 'enable_ai_analysis', 'enable_dependency_analysis',
            'enable_auto_sync', 'prompt'
        ]
        
        for field, value in updates.items():
            if field in allowed_fields and hasattr(warehouse, field):
                setattr(warehouse, field, value)
        
        await self.db.commit()
        await self.db.refresh(warehouse)
        
        logger.info(f"Updated warehouse: {warehouse.id}")
        return warehouse
    
    async def delete_warehouse(self, warehouse_id: str, user_id: str) -> bool:
        """Delete a warehouse."""
        
        warehouse = await self.get_warehouse_by_id(warehouse_id)
        if not warehouse or warehouse.user_id != user_id:
            return False
        
        # Clean up local files
        if warehouse.type == WarehouseType.GIT:
            local_path = self.git_service.get_local_path(warehouse.address, warehouse.branch)
            if os.path.exists(local_path):
                import shutil
                shutil.rmtree(local_path, ignore_errors=True)
        
        # Soft delete
        warehouse.soft_delete()
        await self.db.commit()
        
        logger.info(f"Deleted warehouse: {warehouse.id}")
        return True
    
    async def sync_warehouse(self, warehouse_id: str) -> bool:
        """Manually sync a warehouse."""
        
        warehouse = await self.get_warehouse_by_id(warehouse_id)
        if not warehouse:
            return False
        
        if warehouse.type == WarehouseType.GIT:
            # Start sync in background
            asyncio.create_task(self._sync_git_warehouse(warehouse))
            return True
        
        return False
    
    async def get_warehouse_statistics(self, warehouse_id: str) -> Dict[str, Any]:
        """Get warehouse statistics."""
        
        warehouse = await self.get_warehouse_by_id(warehouse_id)
        if not warehouse:
            return {}
        
        stats = {
            "id": warehouse.id,
            "name": warehouse.name,
            "status": warehouse.status.value,
            "progress": warehouse.progress,
            "stars": warehouse.stars,
            "forks": warehouse.forks,
            "size": warehouse.size,
            "file_count": warehouse.file_count,
            "line_count": warehouse.line_count,
            "last_sync_at": warehouse.last_sync_at,
            "last_analysis_at": warehouse.last_analysis_at,
            "processing_duration": warehouse.processing_duration,
            "created_at": warehouse.created_at,
            "updated_at": warehouse.updated_at
        }
        
        # Add document count
        from ..models.document import Document
        doc_count_result = await self.db.execute(
            select(func.count(Document.id)).where(Document.warehouse_id == warehouse_id)
        )
        stats["document_count"] = doc_count_result.scalar()
        
        return stats
    
    async def _process_warehouse_async(self, warehouse_id: str):
        """Process warehouse asynchronously."""
        try:
            warehouse = await self.get_warehouse_by_id(warehouse_id)
            if not warehouse:
                return
            
            warehouse.start_processing()
            await self.db.commit()
            
            if warehouse.type == WarehouseType.GIT:
                await self._process_git_warehouse(warehouse)
            elif warehouse.type == WarehouseType.FILE:
                await self._process_file_warehouse(warehouse)
            
            warehouse.complete_processing()
            await self.db.commit()
            
            logger.info(f"Warehouse processing completed: {warehouse.id}")
            
        except Exception as e:
            logger.error(f"Warehouse processing failed: {warehouse_id} - {e}")
            
            # Update warehouse with error
            warehouse = await self.get_warehouse_by_id(warehouse_id)
            if warehouse:
                warehouse.fail_processing(str(e))
                await self.db.commit()
    
    async def _process_git_warehouse(self, warehouse: Warehouse):
        """Process a Git warehouse."""
        
        # Clone or update repository
        local_path = await self.git_service.clone_or_update_repository(
            warehouse.address, 
            warehouse.branch
        )
        
        warehouse.update_progress(0.2)
        await self.db.commit()
        
        # Update repository statistics
        await self._update_repository_statistics(warehouse, local_path)
        
        warehouse.update_progress(0.4)
        await self.db.commit()
        
        # Generate documentation if AI analysis is enabled
        if warehouse.enable_ai_analysis:
            await self._generate_documentation(warehouse, local_path)
        
        warehouse.update_progress(0.8)
        await self.db.commit()
        
        # Analyze dependencies if enabled
        if warehouse.enable_dependency_analysis:
            await self._analyze_dependencies(warehouse, local_path)
        
        warehouse.update_progress(1.0)
        warehouse.update_sync_timestamp()
        await self.db.commit()
    
    async def _process_file_warehouse(self, warehouse: Warehouse):
        """Process a file-based warehouse."""
        
        file_path = warehouse.address
        
        # Update file statistics
        if os.path.isfile(file_path):
            warehouse.update_statistics(
                size=os.path.getsize(file_path),
                file_count=1,
                line_count=self._count_lines_in_file(file_path)
            )
        elif os.path.isdir(file_path):
            warehouse.update_statistics(
                size=get_directory_size(file_path),
                file_count=count_files_in_directory(file_path)
            )
        
        warehouse.update_progress(0.5)
        await self.db.commit()
        
        # Generate documentation if AI analysis is enabled
        if warehouse.enable_ai_analysis:
            await self._generate_documentation(warehouse, file_path)
        
        warehouse.update_progress(1.0)
        await self.db.commit()
    
    async def _sync_git_warehouse(self, warehouse: Warehouse):
        """Sync a Git warehouse."""
        try:
            local_path = await self.git_service.update_repository(
                warehouse.address, 
                warehouse.branch
            )
            
            # Update statistics
            await self._update_repository_statistics(warehouse, local_path)
            
            warehouse.update_sync_timestamp()
            await self.db.commit()
            
            logger.info(f"Warehouse synced: {warehouse.id}")
            
        except Exception as e:
            logger.error(f"Warehouse sync failed: {warehouse.id} - {e}")
    
    async def _update_repository_statistics(self, warehouse: Warehouse, local_path: str):
        """Update repository statistics."""
        
        if not os.path.exists(local_path):
            return
        
        # Get repository info from Git service
        repo_info = await self.git_service.get_repository_info(local_path)
        
        # Update warehouse statistics
        warehouse.update_statistics(
            stars=repo_info.get("stars", 0),
            forks=repo_info.get("forks", 0),
            size=get_directory_size(local_path),
            file_count=count_files_in_directory(local_path),
            line_count=self._count_lines_in_directory(local_path)
        )
        
        await self.db.commit()
    
    async def _generate_documentation(self, warehouse: Warehouse, path: str):
        """Generate documentation using AI."""
        
        try:
            # Use AI service to generate documentation
            documentation = await self.ai_service.generate_repository_documentation(
                path, 
                warehouse.address,
                warehouse.branch
            )
            
            # Create document records
            from .document import DocumentService
            doc_service = DocumentService(self.db)
            
            for doc_data in documentation:
                await doc_service.create_document(
                    warehouse_id=warehouse.id,
                    title=doc_data["title"],
                    content=doc_data["content"],
                    doc_type=doc_data.get("type", "custom"),
                    language=doc_data.get("language", "zh-CN")
                )
            
            logger.info(f"Generated {len(documentation)} documents for warehouse: {warehouse.id}")
            
        except Exception as e:
            logger.error(f"Documentation generation failed: {warehouse.id} - {e}")
    
    async def _analyze_dependencies(self, warehouse: Warehouse, path: str):
        """Analyze code dependencies."""
        
        try:
            # Use AI service to analyze dependencies
            dependencies = await self.ai_service.analyze_code_dependencies(path)
            
            # Store dependency information in warehouse metadata
            warehouse.set_metadata({
                "dependencies": dependencies,
                "dependency_analysis_at": datetime.utcnow().isoformat()
            })
            
            await self.db.commit()
            
            logger.info(f"Analyzed dependencies for warehouse: {warehouse.id}")
            
        except Exception as e:
            logger.error(f"Dependency analysis failed: {warehouse.id} - {e}")
    
    def _count_lines_in_file(self, file_path: str) -> int:
        """Count lines in a single file."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return sum(1 for _ in f)
        except Exception:
            return 0
    
    def _count_lines_in_directory(self, directory: str) -> int:
        """Count total lines in all files in directory."""
        total_lines = 0
        
        for root, dirs, files in os.walk(directory):
            # Skip hidden directories and common ignore patterns
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'venv']]
            
            for file in files:
                if file.startswith('.'):
                    continue
                
                file_path = os.path.join(root, file)
                total_lines += self._count_lines_in_file(file_path)
        
        return total_lines
