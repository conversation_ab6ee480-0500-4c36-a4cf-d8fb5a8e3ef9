#!/usr/bin/env python3
"""
KoalaWiki Installation Test Script

This script tests if KoalaWiki is properly installed and configured.
"""

import sys
import os
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))


def test_imports():
    """Test if all required modules can be imported."""
    print("Testing imports...")
    
    try:
        import fastapi
        print("✅ FastAPI")
    except ImportError:
        print("❌ FastAPI - run: pip install fastapi")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn")
    except ImportError:
        print("❌ Uvicorn - run: pip install uvicorn")
        return False
    
    try:
        import sqlalchemy
        print("✅ SQLAlchemy")
    except ImportError:
        print("❌ SQLAlchemy - run: pip install sqlalchemy")
        return False
    
    try:
        import aiosqlite
        print("✅ aiosqlite")
    except ImportError:
        print("❌ aiosqlite - run: pip install aiosqlite")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv")
    except ImportError:
        print("❌ python-dotenv - run: pip install python-dotenv")
        return False
    
    try:
        from loguru import logger
        print("✅ loguru")
    except ImportError:
        print("❌ loguru - run: pip install loguru")
        return False
    
    return True


def test_koala_wiki_imports():
    """Test if KoalaWiki modules can be imported."""
    print("\nTesting KoalaWiki modules...")
    
    try:
        from koala_wiki.core.config import settings
        print("✅ Core config")
    except ImportError as e:
        print(f"❌ Core config: {e}")
        return False
    
    try:
        from koala_wiki.core.database import Base
        print("✅ Database models")
    except ImportError as e:
        print(f"❌ Database models: {e}")
        return False
    
    try:
        from koala_wiki.models.user import User
        print("✅ User model")
    except ImportError as e:
        print(f"❌ User model: {e}")
        return False
    
    try:
        from koala_wiki.models.warehouse import Warehouse
        print("✅ Warehouse model")
    except ImportError as e:
        print(f"❌ Warehouse model: {e}")
        return False
    
    try:
        from koala_wiki.main import create_app
        print("✅ Main application")
    except ImportError as e:
        print(f"❌ Main application: {e}")
        return False
    
    return True


def test_configuration():
    """Test configuration."""
    print("\nTesting configuration...")
    
    try:
        from koala_wiki.core.config import settings
        
        print(f"✅ App name: {settings.app_name}")
        print(f"✅ App version: {settings.app_version}")
        print(f"✅ Database type: {settings.db_type}")
        print(f"✅ Database URL: {settings.database_url}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


def test_database_connection():
    """Test database connection."""
    print("\nTesting database connection...")
    
    try:
        import asyncio
        from koala_wiki.core.database import db_manager
        
        async def test_db():
            try:
                db_manager.initialize()
                print("✅ Database manager initialized")
                return True
            except Exception as e:
                print(f"❌ Database connection failed: {e}")
                return False
        
        result = asyncio.run(test_db())
        return result
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


def test_api_creation():
    """Test API application creation."""
    print("\nTesting API creation...")
    
    try:
        from koala_wiki.main import create_app
        
        app = create_app()
        print("✅ FastAPI application created")
        print(f"✅ App title: {app.title}")
        print(f"✅ App version: {app.version}")
        
        return True
    except Exception as e:
        print(f"❌ API creation failed: {e}")
        return False


def main():
    """Main test function."""
    print("🐨 KoalaWiki Installation Test")
    print("=" * 40)
    
    tests = [
        ("Basic Dependencies", test_imports),
        ("KoalaWiki Modules", test_koala_wiki_imports),
        ("Configuration", test_configuration),
        ("Database Connection", test_database_connection),
        ("API Creation", test_api_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! KoalaWiki is ready to use.")
        print("\nNext steps:")
        print("1. python -m koala_wiki init-database")
        print("2. python -m koala_wiki create-admin")
        print("3. python -m koala_wiki serve")
        return True
    else:
        print("⚠️  Some tests failed. Please check the installation.")
        print("\nTry:")
        print("1. pip install -r requirements-minimal.txt")
        print("2. Check Python version (3.9+ required)")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
