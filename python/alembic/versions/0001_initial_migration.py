"""Initial migration

Revision ID: 0001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '0001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create users table
    op.create_table('users',
        sa.Column('id', sa.String(36), nullable=False),
        sa.<PERSON>umn('username', sa.String(50), nullable=False),
        sa.<PERSON>umn('email', sa.String(255), nullable=False),
        sa.Column('hashed_password', sa.String(255), nullable=False),
        sa.Column('full_name', sa.String(100), nullable=True),
        sa.Column('avatar_url', sa.String(500), nullable=True),
        sa.Column('bio', sa.Text(), nullable=True),
        sa.Column('is_active', sa.<PERSON>(), nullable=False, default=True),
        sa.Column('is_superuser', sa.<PERSON>(), nullable=False, default=False),
        sa.Column('is_verified', sa.<PERSON>(), nullable=False, default=False),
        sa.Column('role', sa.String(20), nullable=False, default='user'),
        sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('login_count', sa.Integer(), nullable=False, default=0),
        sa.Column('api_key', sa.String(255), nullable=True),
        sa.Column('api_key_expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('language', sa.String(10), nullable=False, default='zh-CN'),
        sa.Column('timezone', sa.String(50), nullable=False, default='UTC'),
        sa.Column('theme', sa.String(20), nullable=False, default='light'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_api_key'), 'users', ['api_key'], unique=True)

    # Create warehouses table
    op.create_table('warehouses',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('organization_name', sa.String(255), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('address', sa.String(500), nullable=False),
        sa.Column('branch', sa.String(100), nullable=False, default='main'),
        sa.Column('version', sa.String(50), nullable=True),
        sa.Column('type', sa.Enum('GIT', 'FILE', 'CUSTOM', name='warehousetype'), nullable=False, default='GIT'),
        sa.Column('status', sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', name='warehousestatus'), nullable=False, default='PENDING'),
        sa.Column('error', sa.Text(), nullable=True),
        sa.Column('progress', sa.Float(), nullable=False, default=0.0),
        sa.Column('prompt', sa.Text(), nullable=True),
        sa.Column('optimized_directory_structure', sa.Text(), nullable=True),
        sa.Column('stars', sa.Integer(), nullable=False, default=0),
        sa.Column('forks', sa.Integer(), nullable=False, default=0),
        sa.Column('size', sa.Integer(), nullable=False, default=0),
        sa.Column('file_count', sa.Integer(), nullable=False, default=0),
        sa.Column('line_count', sa.Integer(), nullable=False, default=0),
        sa.Column('last_sync_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_analysis_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('processing_started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('processing_completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('enable_auto_sync', sa.Boolean(), nullable=False, default=True),
        sa.Column('enable_ai_analysis', sa.Boolean(), nullable=False, default=True),
        sa.Column('enable_dependency_analysis', sa.Boolean(), nullable=False, default=False),
        sa.Column('user_id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('metadata', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_warehouses_id'), 'warehouses', ['id'], unique=False)
    op.create_index(op.f('ix_warehouses_name'), 'warehouses', ['name'], unique=False)
    op.create_index(op.f('ix_warehouses_user_id'), 'warehouses', ['user_id'], unique=False)

    # Create documents table
    op.create_table('documents',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('title', sa.String(500), nullable=False),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('summary', sa.Text(), nullable=True),
        sa.Column('type', sa.Enum('README', 'API_DOC', 'TUTORIAL', 'GUIDE', 'REFERENCE', 'CHANGELOG', 'CUSTOM', name='documenttype'), nullable=False, default='CUSTOM'),
        sa.Column('language', sa.String(10), nullable=False, default='zh-CN'),
        sa.Column('file_path', sa.String(1000), nullable=True),
        sa.Column('git_path', sa.String(1000), nullable=False),
        sa.Column('status', sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', name='documentstatus'), nullable=False, default='PENDING'),
        sa.Column('error', sa.Text(), nullable=True),
        sa.Column('progress', sa.Float(), nullable=False, default=0.0),
        sa.Column('word_count', sa.Integer(), nullable=False, default=0),
        sa.Column('character_count', sa.Integer(), nullable=False, default=0),
        sa.Column('reading_time', sa.Integer(), nullable=False, default=0),
        sa.Column('view_count', sa.Integer(), nullable=False, default=0),
        sa.Column('like_count', sa.Integer(), nullable=False, default=0),
        sa.Column('comment_count', sa.Integer(), nullable=False, default=0),
        sa.Column('ai_model', sa.String(100), nullable=True),
        sa.Column('generation_prompt', sa.Text(), nullable=True),
        sa.Column('generation_time', sa.Float(), nullable=True),
        sa.Column('version', sa.String(50), nullable=False, default='1.0'),
        sa.Column('parent_document_id', sa.String(36), nullable=True),
        sa.Column('last_update', sa.DateTime(timezone=True), nullable=True),
        sa.Column('published_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('warehouse_id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('metadata', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['parent_document_id'], ['documents.id'], ),
        sa.ForeignKeyConstraint(['warehouse_id'], ['warehouses.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_documents_id'), 'documents', ['id'], unique=False)
    op.create_index(op.f('ix_documents_warehouse_id'), 'documents', ['warehouse_id'], unique=False)

    # Create document_catalogs table
    op.create_table('document_catalogs',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('path', sa.String(1000), nullable=False),
        sa.Column('parent_id', sa.String(36), nullable=True),
        sa.Column('level', sa.Integer(), nullable=False, default=0),
        sa.Column('sort_order', sa.Integer(), nullable=False, default=0),
        sa.Column('is_folder', sa.Boolean(), nullable=False, default=True),
        sa.Column('icon', sa.String(100), nullable=True),
        sa.Column('language', sa.String(10), nullable=False, default='zh-CN'),
        sa.Column('document_id', sa.String(36), nullable=True),
        sa.Column('warehouse_id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
        sa.ForeignKeyConstraint(['parent_id'], ['document_catalogs.id'], ),
        sa.ForeignKeyConstraint(['warehouse_id'], ['warehouses.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_document_catalogs_id'), 'document_catalogs', ['id'], unique=False)
    op.create_index(op.f('ix_document_catalogs_warehouse_id'), 'document_catalogs', ['warehouse_id'], unique=False)


def downgrade() -> None:
    op.drop_table('document_catalogs')
    op.drop_table('documents')
    op.drop_table('warehouses')
    op.drop_table('users')
