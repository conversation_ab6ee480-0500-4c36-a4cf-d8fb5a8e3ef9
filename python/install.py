#!/usr/bin/env python3
"""
KoalaWiki Python Installation Script

This script helps you set up KoalaWiki Python environment quickly.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(command, check=True):
    """Run a shell command."""
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ Python 3.9+ is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def check_git():
    """Check if Git is installed."""
    if run_command("git --version", check=False):
        print("✅ Git is installed")
        return True
    else:
        print("❌ Git is not installed")
        print("Please install Git: https://git-scm.com/downloads")
        return False


def create_virtual_environment():
    """Create virtual environment."""
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True
    
    print("Creating virtual environment...")
    if run_command(f"{sys.executable} -m venv venv"):
        print("✅ Virtual environment created")
        return True
    else:
        print("❌ Failed to create virtual environment")
        return False


def get_pip_command():
    """Get the correct pip command for the platform."""
    if platform.system() == "Windows":
        return "venv\\Scripts\\pip"
    else:
        return "venv/bin/pip"


def install_dependencies():
    """Install Python dependencies."""
    pip_cmd = get_pip_command()
    
    print("Installing dependencies...")
    
    # Try minimal requirements first
    if Path("requirements-minimal.txt").exists():
        print("Installing minimal requirements...")
        if run_command(f"{pip_cmd} install -r requirements-minimal.txt"):
            print("✅ Minimal dependencies installed")
            return True
    
    # Fallback to full requirements
    if Path("requirements.txt").exists():
        print("Installing full requirements...")
        if run_command(f"{pip_cmd} install -r requirements.txt"):
            print("✅ Dependencies installed")
            return True
    
    print("❌ Failed to install dependencies")
    return False


def create_env_file():
    """Create .env file from template."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if env_example.exists():
        print("Creating .env file from template...")
        try:
            content = env_example.read_text()
            env_file.write_text(content)
            print("✅ .env file created")
            print("⚠️  Please edit .env file with your configuration")
            return True
        except Exception as e:
            print(f"❌ Failed to create .env file: {e}")
            return False
    else:
        print("❌ .env.example not found")
        return False


def initialize_database():
    """Initialize the database."""
    python_cmd = get_pip_command().replace("pip", "python")
    
    print("Initializing database...")
    if run_command(f"{python_cmd} -m koala_wiki init-database"):
        print("✅ Database initialized")
        return True
    else:
        print("❌ Failed to initialize database")
        return False


def main():
    """Main installation function."""
    print("🐨 KoalaWiki Python Installation")
    print("=" * 50)
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    if not check_git():
        return False
    
    # Setup environment
    if not create_virtual_environment():
        return False
    
    if not install_dependencies():
        return False
    
    if not create_env_file():
        return False
    
    # Initialize application
    if not initialize_database():
        print("⚠️  Database initialization failed, but you can try manually later")
    
    print("\n🎉 Installation completed!")
    print("\nNext steps:")
    print("1. Edit .env file with your configuration (especially CHAT_API_KEY)")
    print("2. Create an admin user:")
    
    if platform.system() == "Windows":
        print("   venv\\Scripts\\python -m koala_wiki create-admin")
        print("3. Start the server:")
        print("   venv\\Scripts\\python -m koala_wiki serve")
    else:
        print("   venv/bin/python -m koala_wiki create-admin")
        print("3. Start the server:")
        print("   venv/bin/python -m koala_wiki serve")
    
    print("4. Open http://localhost:8000/docs in your browser")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
