#!/usr/bin/env python3
"""
KoalaWiki Python Demo Script

This script demonstrates the basic functionality of the KoalaWiki Python implementation.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from koala_wiki.core.config import settings
from koala_wiki.core.database import init_db, get_db
from koala_wiki.services.auth import AuthService
from koala_wiki.services.warehouse import WarehouseService
from koala_wiki.models.user import User


async def create_demo_user():
    """Create a demo user."""
    print("Creating demo user...")
    
    async for db in get_db():
        try:
            user = await AuthService.create_user(
                username="demo",
                email="<EMAIL>",
                password="demo123",
                full_name="Demo User",
                role="user",
                db=db
            )
            print(f"✓ Created demo user: {user.username} ({user.id})")
            return user
        except Exception as e:
            print(f"✗ Failed to create demo user: {e}")
            return None


async def create_demo_warehouse(user: User):
    """Create a demo warehouse."""
    print("Creating demo warehouse...")
    
    async for db in get_db():
        try:
            warehouse_service = WarehouseService(db)
            
            # Create a simple file warehouse using the current directory
            warehouse = await warehouse_service.create_file_warehouse(
                user=user,
                name="KoalaWiki Python Demo",
                file_path=str(Path(__file__).parent),
                description="Demo warehouse for KoalaWiki Python implementation",
                enable_ai_analysis=False  # Disable AI for demo
            )
            
            print(f"✓ Created demo warehouse: {warehouse.name} ({warehouse.id})")
            return warehouse
        except Exception as e:
            print(f"✗ Failed to create demo warehouse: {e}")
            return None


async def main():
    """Main demo function."""
    print("🐨 KoalaWiki Python Demo")
    print("=" * 50)
    
    # Initialize database
    print("Initializing database...")
    try:
        await init_db()
        print("✓ Database initialized")
    except Exception as e:
        print(f"✗ Database initialization failed: {e}")
        return
    
    # Create demo user
    user = await create_demo_user()
    if not user:
        return
    
    # Create demo warehouse
    warehouse = await create_demo_warehouse(user)
    if not warehouse:
        return
    
    print("\n🎉 Demo completed successfully!")
    print("\nNext steps:")
    print("1. Start the server: python -m koala_wiki serve")
    print("2. Open http://localhost:8000/docs in your browser")
    print("3. Login with username 'demo' and password 'demo123'")
    print("4. Explore the API endpoints")
    
    print(f"\nDemo user credentials:")
    print(f"  Username: {user.username}")
    print(f"  Password: demo123")
    print(f"  Email: {user.email}")
    
    print(f"\nDemo warehouse:")
    print(f"  ID: {warehouse.id}")
    print(f"  Name: {warehouse.name}")
    print(f"  Status: {warehouse.status.value}")


if __name__ == "__main__":
    # Set up environment for demo
    os.environ.setdefault("DB_TYPE", "sqlite")
    os.environ.setdefault("DB_CONNECTION_STRING", "sqlite:///./demo.db")
    os.environ.setdefault("SECRET_KEY", "demo_secret_key_change_in_production")
    os.environ.setdefault("CHAT_API_KEY", "demo_key")
    
    asyncio.run(main())
