# PyCharm 中启动 KoalaWiki 指南

## 🚀 快速开始

### 1. 打开项目
1. 启动 PyCharm
2. 选择 `File` → `Open`
3. 选择 `python` 文件夹作为项目根目录
4. 点击 `OK`

### 2. 配置 Python 解释器
1. 打开 `File` → `Settings` (Windows/Linux) 或 `PyCharm` → `Preferences` (macOS)
2. 导航到 `Project: python` → `Python Interpreter`
3. 点击齿轮图标 → `Add...`
4. 选择 `Virtualenv Environment` → `New environment`
5. 设置位置为项目目录下的 `venv` 文件夹
6. 点击 `OK`

### 3. 安装依赖
在 PyCharm 终端中运行：
```bash
pip install -r requirements-minimal.txt
```

或者：
1. 打开 `requirements-minimal.txt` 文件
2. PyCharm 会提示安装依赖，点击 `Install requirements`

### 4. 配置环境变量
1. 复制 `.env.pycharm` 为 `.env`：
```bash
cp .env.pycharm .env
```
2. 编辑 `.env` 文件，设置您的 OpenAI API Key：
```
CHAT_API_KEY=your_actual_openai_api_key_here
```

### 5. 运行项目

#### 方法 1: 使用预配置的运行配置
1. 在 PyCharm 顶部工具栏找到运行配置下拉菜单
2. 选择以下配置之一：
   - **Quick Start** - 快速启动演示
   - **KoalaWiki Server** - 完整服务器
   - **Test Installation** - 测试安装

#### 方法 2: 直接运行脚本
1. 右键点击 `run_server.py`
2. 选择 `Run 'run_server'`

#### 方法 3: 使用终端
在 PyCharm 终端中运行：
```bash
python run_server.py
```

## 📋 运行配置说明

### Quick Start
- **用途**: 快速演示，无需完整配置
- **特点**: 使用内存数据库，自动初始化
- **适用**: 第一次体验项目

### KoalaWiki Server
- **用途**: 完整的开发服务器
- **特点**: 支持热重载，完整功能
- **适用**: 日常开发

### Test Installation
- **用途**: 测试安装是否正确
- **特点**: 检查所有依赖和配置
- **适用**: 排查问题

## 🛠 开发配置

### 源码目录结构
```
python/
├── src/koala_wiki/          # 主要源代码 (已标记为源码根目录)
├── tests/                   # 测试代码 (已标记为测试根目录)
├── .idea/                   # PyCharm 配置文件
├── run_server.py           # PyCharm 启动脚本
└── .env                    # 环境变量配置
```

### 调试配置
1. 在代码中设置断点
2. 右键点击 `run_server.py`
3. 选择 `Debug 'run_server'`

### 测试配置
1. 右键点击 `tests` 文件夹
2. 选择 `Run 'pytest in tests'`

## 🔧 常见问题

### 问题 1: 模块导入错误
**解决方案**:
1. 确保 `src` 文件夹被标记为源码根目录
2. 检查 Python 解释器配置
3. 重启 PyCharm

### 问题 2: 依赖安装失败
**解决方案**:
1. 使用最小化依赖：`pip install -r requirements-minimal.txt`
2. 检查 Python 版本 (需要 3.9+)
3. 更新 pip：`pip install --upgrade pip`

### 问题 3: 数据库错误
**解决方案**:
1. 删除现有数据库文件：`rm *.db`
2. 运行初始化：`python -m koala_wiki init-database`
3. 或使用 Quick Start 模式

### 问题 4: API Key 未设置
**解决方案**:
1. 编辑 `.env` 文件
2. 设置 `CHAT_API_KEY=your_actual_api_key`
3. 重启服务器

## 📖 开发工作流

### 1. 日常开发
1. 启动 "KoalaWiki Server" 配置
2. 修改代码 (支持热重载)
3. 在浏览器中测试: http://localhost:8000/docs

### 2. 添加新功能
1. 在 `src/koala_wiki/` 中添加代码
2. 在 `tests/` 中添加测试
3. 运行测试验证功能

### 3. 调试问题
1. 设置断点
2. 使用调试模式启动
3. 逐步调试代码

## 🌐 访问地址

启动成功后，您可以访问：

- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **根路径**: http://localhost:8000/

## 📝 开发提示

1. **代码格式化**: PyCharm 会自动格式化代码
2. **类型提示**: 项目使用 mypy 进行类型检查
3. **日志查看**: 日志会输出到控制台和文件
4. **热重载**: 修改代码后服务器会自动重启

## 🎯 下一步

1. 创建管理员用户：
```bash
python -m koala_wiki create-admin
```

2. 开始开发您的功能

3. 运行测试：
```bash
pytest
```

祝您开发愉快！🐨
