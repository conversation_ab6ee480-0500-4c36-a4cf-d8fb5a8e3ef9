#!/usr/bin/env python3
"""
KoalaWiki Quick Start Script

This script provides a quick way to start KoalaWiki with minimal setup.
"""

import os
import sys
import tempfile
import asyncio
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))


async def quick_start():
    """Quick start with minimal setup."""
    print("🐨 KoalaWiki Quick Start")
    print("=" * 30)
    
    # Set minimal environment variables
    os.environ.setdefault("DB_TYPE", "sqlite")
    os.environ.setdefault("DB_CONNECTION_STRING", "sqlite:///./quickstart.db")
    os.environ.setdefault("SECRET_KEY", "quickstart_secret_key_change_in_production")
    os.environ.setdefault("CHAT_API_KEY", "demo_key")
    os.environ.setdefault("DEBUG", "true")
    os.environ.setdefault("LOG_LEVEL", "INFO")
    
    try:
        from koala_wiki.core.database import init_db
        from koala_wiki.main import app
        import uvicorn
        
        print("Initializing database...")
        await init_db()
        print("✅ Database initialized")
        
        print("\n🚀 Starting KoalaWiki server...")
        print("📖 API Documentation: http://localhost:8000/docs")
        print("🔍 Health Check: http://localhost:8000/health")
        print("\n⚠️  Note: This is a demo setup. For production, please:")
        print("   1. Set up proper environment variables")
        print("   2. Use a real OpenAI API key")
        print("   3. Use PostgreSQL for database")
        print("\nPress Ctrl+C to stop the server")
        
        # Start the server
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            log_level="info",
            reload=False
        )
        
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("\nPlease install dependencies first:")
        print("pip install fastapi uvicorn sqlalchemy aiosqlite python-dotenv loguru")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True


def main():
    """Main function."""
    try:
        asyncio.run(quick_start())
    except KeyboardInterrupt:
        print("\n\n👋 KoalaWiki stopped. Thank you for trying it!")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
