#!/usr/bin/env python3
"""
PyCharm 启动脚本 - KoalaWiki 服务器

这个脚本专门为在 PyCharm 中启动 KoalaWiki 服务器而设计。
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加 src 到 Python 路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

# 设置环境变量
os.environ.setdefault("PYTHONPATH", str(src_path))
os.environ.setdefault("DB_TYPE", "sqlite")
os.environ.setdefault("DB_CONNECTION_STRING", "sqlite:///./koala_wiki.db")
os.environ.setdefault("SECRET_KEY", "dev_secret_key_change_in_production")
os.environ.setdefault("CHAT_API_KEY", "your_openai_api_key_here")
os.environ.setdefault("DEBUG", "true")
os.environ.setdefault("RELOAD", "true")
os.environ.setdefault("LOG_LEVEL", "INFO")


async def init_database_if_needed():
    """如果需要的话初始化数据库"""
    try:
        from koala_wiki.core.database import init_db
        await init_db()
        print("✅ 数据库已初始化")
    except Exception as e:
        print(f"⚠️ 数据库初始化失败: {e}")
        print("请手动运行: python -m koala_wiki init-database")


def main():
    """主函数"""
    print("🐨 启动 KoalaWiki 开发服务器")
    print("=" * 50)
    
    # 检查环境
    print(f"Python 版本: {sys.version}")
    print(f"项目路径: {project_root}")
    print(f"源码路径: {src_path}")
    
    # 初始化数据库
    try:
        asyncio.run(init_database_if_needed())
    except Exception as e:
        print(f"数据库初始化错误: {e}")
    
    # 启动服务器
    try:
        import uvicorn
        from koala_wiki.main import app
        
        print("\n🚀 启动服务器...")
        print("📖 API 文档: http://localhost:8000/docs")
        print("🔍 健康检查: http://localhost:8000/health")
        print("🛑 按 Ctrl+C 停止服务器")
        
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            reload=True,  # 开发模式下启用热重载
            log_level="info",
            access_log=True
        )
        
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装依赖: pip install -r requirements-minimal.txt")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


if __name__ == "__main__":
    main()
