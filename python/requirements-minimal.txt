# Minimal requirements for KoalaWiki Python
# Core web framework
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Database
sqlalchemy>=2.0.23
alembic>=1.13.1
aiosqlite>=0.19.0  # SQLite for development

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# AI and ML
openai>=1.3.7

# Git operations
GitPython>=3.1.40

# Utilities
python-dotenv>=1.0.0
click>=8.1.7
loguru>=0.7.2

# HTTP client
httpx>=0.25.2
