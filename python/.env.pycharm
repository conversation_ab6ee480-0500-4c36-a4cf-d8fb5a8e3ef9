# PyCharm 开发环境配置
# 复制这个文件为 .env 并修改相应的值

# 应用配置
APP_NAME=KoalaWiki
APP_VERSION=0.9.0
DEBUG=true
HOST=127.0.0.1
PORT=8000
RELOAD=true

# 数据库配置 (开发环境使用 SQLite)
DB_TYPE=sqlite
DB_CONNECTION_STRING=sqlite:///./koala_wiki_dev.db

# AI 配置 (请设置您的 OpenAI API Key)
CHAT_MODEL=gpt-4-turbo-preview
ANALYSIS_MODEL=gpt-4-turbo-preview
CHAT_API_KEY=your_openai_api_key_here
ENDPOINT=https://api.openai.com/v1
LANGUAGE=中文

# 仓库配置
KOALAWIKI_REPOSITORIES=./repositories
UPDATE_INTERVAL=5
ENABLE_INCREMENTAL_UPDATE=true
ENABLE_CODED_DEPENDENCY_ANALYSIS=false
ENABLE_SMART_FILTER=true

# 任务配置
TASK_MAX_SIZE_PER_USER=5
REPAIR_MERMAID=1

# 安全配置
SECRET_KEY=dev_secret_key_change_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis 配置 (可选，用于后台任务)
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/koala_wiki_dev.log

# CORS 配置
CORS_ORIGINS=["http://localhost:3000", "http://localhost:3333", "http://127.0.0.1:3000"]

# 功能开关
ENABLE_AGENT_TOOL=true
ENABLE_MCP=true
ENABLE_I18N=true
ENABLE_STATISTICS=true
ENABLE_MINIMAP=true

# 文件上传配置
MAX_FILE_SIZE=100MB
ALLOWED_EXTENSIONS=[".md", ".txt", ".py", ".js", ".ts", ".java", ".go", ".cs", ".cpp", ".c", ".h"]

# 缓存配置
CACHE_TTL=3600
MEMORY_CACHE_SIZE=1000
