version: '3.8'

services:
  koala-wiki:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DB_TYPE=postgresql
      - DB_CONNECTION_STRING=postgresql+asyncpg://koala:password@postgres:5432/koala_wiki
      - REDIS_URL=redis://redis:6379/0
      - CHAT_API_KEY=${CHAT_API_KEY}
      - SECRET_KEY=${SECRET_KEY:-your_secret_key_change_in_production}
    volumes:
      - ./repositories:/app/repositories
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=koala_wiki
      - POSTGRES_USER=koala
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Optional: pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    ports:
      - "5050:80"
    depends_on:
      - postgres
    restart: unless-stopped
    profiles:
      - tools

volumes:
  postgres_data:
  redis_data:
