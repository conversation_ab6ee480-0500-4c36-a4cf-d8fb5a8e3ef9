name: <PERSON>uild and Push Docker Images
on:
  workflow_dispatch:
  push:
    branches: [ main ]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'web-site/package.json'
      
      - name: Install frontend dependencies
        run: |
          cd web-site
          npm i 
      
      - name: Build frontend
        run: |
          cd web-site
          npm run build
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Login to Docker Registry
        uses: docker/login-action@v3
        with:
          registry: crpi-j9ha7sxwhatgtvj4.cn-shenzhen.personal.cr.aliyuncs.com
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      
      - name: Extract version from Directory.Packages.props
        id: get_version
        run: |
          # Extract Version and VersionSuffix from Directory.Packages.props
          VERSION_TEMPLATE=$(grep -oP '(?<=<Version>)[^<]*' Directory.Packages.props)
          VERSION_SUFFIX=$(grep -oP '(?<=<VersionSuffix>)[^<]*' Directory.Packages.props)
          
          # Replace $(VersionSuffix) with actual value
          VERSION=$(echo "$VERSION_TEMPLATE" | sed "s/\$(VersionSuffix)/$VERSION_SUFFIX/g")
          
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Extracted version template: $VERSION_TEMPLATE"
          echo "Version suffix: $VERSION_SUFFIX"
          echo "Final version: $VERSION"
      
      - name: Build and push koala-wiki
        uses: docker/build-push-action@v5
        with:
          context: .
          file: src/KoalaWiki/Dockerfile
          platforms: linux/amd64
          push: true
          tags: |
            crpi-j9ha7sxwhatgtvj4.cn-shenzhen.personal.cr.aliyuncs.com/koala-ai/koala-wiki:latest
            crpi-j9ha7sxwhatgtvj4.cn-shenzhen.personal.cr.aliyuncs.com/koala-ai/koala-wiki:${{ steps.get_version.outputs.version }}
