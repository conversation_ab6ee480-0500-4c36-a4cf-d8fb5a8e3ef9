# KOALAWIKI_REPOSITORIES=/repositories
# TASK_MAX_SIZE_PER_USER=5 # 每个用户AI处理文档生成的最大数量
# REPAIR_MERMAID=1 # 是否进行Mermaid修复，1修复，其余不修复
# CHAT_MODEL=deepseek-chat	 # 必须要支持function的模型
# ANALYSIS_MODEL=deepseek-chat	# 分析模型，用于生成仓库目录结构，这个很重要，模型越强，生成的目录结构越好，为空则使用ChatModel
# CHAT_API_KEY=sk-989a64be6eeb4aa884327d5e0a18b173
# LANGUAGE=中文 # 设置生成语言默认为“中文”, 英文可以填写 English 或 英文
# ENDPOINT=https://api.deepseek.com
# DB_TYPE=sqlite3
# DB_CONNECTION_STRING=Data Source=/Users/<USER>/project/self/OpenDeepWiki/src/KoalaWiki/KoalaWiki.db
# UPDATE_INTERVAL=5 # 仓库增量更新间隔，单位天
# EnableSmartFilter=true # 是否启用智能过滤，这可能影响AI得到仓库的文件目录
# ENABLE_INCREMENTAL_UPDATE=true # 是否启用增量更新
# ENABLE_CODED_DEPENDENCY_ANALYSIS=false # 是否启用代码依赖分析？这可能会对代码的质量产生影响。
# ENABLE_WAREHOUSE_FUNCTION_PROMPT_TASK=true # 是否启用MCP Prompt生成
# ENABLE_WAREHOUSE_DESCRIPTION_TASK=true # 是否启用仓库Description生成


KOALAWIKI_REPOSITORIES=/repositories
TASK_MAX_SIZE_PER_USER=5 # 每个用户AI处理文档生成的最大数量
REPAIR_MERMAID=1 # 是否进行Mermaid修复，1修复，其余不修复
CHAT_MODEL=Qwen2.5-VL-72B-Instruct  # 必须要支持function的模型
ANALYSIS_MODEL=Qwen2.5-VL-72B-Instruct  # 分析模型，用于生成仓库目录结构，这个很重要，模型越强，生成的目录结构越好，为空则使用ChatModel
CHAT_API_KEY=d360072c5b202b72013fed8a72c8421cce85b581765a79f8a16d7f72bc7214e7
LANGUAGE=中文 # 设置生成语言默认为“中文”, 英文可以填写 English 或 英文
ENDPOINT=https://compass.llm.shopee.io/compass-api/v1
DB_TYPE=sqlite3
DB_CONNECTION_STRING=Data Source=/Users/<USER>/project/self/OpenDeepWiki/src/KoalaWiki/KoalaWiki1.db
UPDATE_INTERVAL=5 # 仓库增量更新间隔，单位天
EnableSmartFilter=true # 是否启用智能过滤，这可能影响AI得到仓库的文件目录
ENABLE_INCREMENTAL_UPDATE=true # 是否启用增量更新
ENABLE_CODED_DEPENDENCY_ANALYSIS=false # 是否启用代码依赖分析？这可能会对代码的质量产生影响。
ENABLE_WAREHOUSE_FUNCTION_PROMPT_TASK=true # 是否启用MCP Prompt生成
ENABLE_WAREHOUSE_DESCRIPTION_TASK=true # 是否启用仓库Description生成

