using System.Text;
using System.Text.RegularExpressions;

namespace KoalaWiki.Handlers;

/// <summary>
/// HTTP 消息处理器，用于修复 ChatFinishReason 兼容性问题
/// 将非标准的 finish_reason 值转换为标准值
/// </summary>
public class ChatFinishReasonFixHandler : DelegatingHandler
{
    // 定义 finish_reason 映射规则
    private static readonly Dictionary<string, string> FinishReasonMappings = new(StringComparer.OrdinalIgnoreCase)
    {
        { "end", "stop" },           // 主要的兼容性问题
        { "finished", "stop" },     // 可能的其他变体
        { "complete", "stop" },     // 另一种可能的表示
        { "done", "stop" },         // 完成的另一种表示
        { "max_tokens", "length" }, // 令牌限制的不同表示
        { "token_limit", "length" }, // 令牌限制的变体
    };

    private static readonly Regex FinishReasonRegex = new(
        @"""finish_reason""\s*:\s*""([^""]+)""", 
        RegexOptions.Compiled | RegexOptions.IgnoreCase);

    public ChatFinishReasonFixHandler(HttpMessageHandler innerHandler) : base(innerHandler)
    {
    }

    public ChatFinishReasonFixHandler() : base()
    {
    }

    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request, 
        CancellationToken cancellationToken)
    {
        var response = await base.SendAsync(request, cancellationToken);

        // 只处理成功的响应
        if (response.IsSuccessStatusCode && response.Content != null)
        {
            var contentType = response.Content.Headers.ContentType?.MediaType;
            
            // 只处理 JSON 响应
            if (string.Equals(contentType, "application/json", StringComparison.OrdinalIgnoreCase))
            {
                var originalContent = await response.Content.ReadAsStringAsync(cancellationToken);
                
                // 检查并修复非标准的 finish_reason 值
                var matches = FinishReasonRegex.Matches(originalContent);
                if (matches.Count > 0)
                {
                    var fixedContent = originalContent;
                    var hasChanges = false;
                    
                    foreach (Match match in matches)
                    {
                        var originalValue = match.Groups[1].Value;
                        
                        // 检查是否需要映射
                        if (FinishReasonMappings.TryGetValue(originalValue, out var mappedValue))
                        {
                            Console.WriteLine($"检测到非标准的 finish_reason: '{originalValue}'，正在转换为 '{mappedValue}'");
                            
                            // 替换当前匹配的 finish_reason 值
                            fixedContent = fixedContent.Replace(match.Value, 
                                $@"""finish_reason"": ""{mappedValue}""");
                            hasChanges = true;
                            
                            Log.Logger.Debug("ChatFinishReason 修复: '{OriginalValue}' -> '{MappedValue}'", 
                                originalValue, mappedValue);
                        }
                    }
                    
                    // 如果有修改，更新响应内容
                    if (hasChanges)
                    {
                        // 创建新的响应内容
                        var newContent = new StringContent(fixedContent, Encoding.UTF8, "application/json");
                        
                        // 复制原始的 Content-Type headers
                        foreach (var header in response.Content.Headers)
                        {
                            newContent.Headers.TryAddWithoutValidation(header.Key, header.Value);
                        }
                        
                        response.Content = newContent;
                        Log.Logger.Information("ChatFinishReason 兼容性修复完成");
                    }
                }
            }
        }

        return response;
    }
}
