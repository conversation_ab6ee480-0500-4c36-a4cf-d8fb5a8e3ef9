namespace KoalaWiki.Extensions;

/// <summary>
/// ChatFinishReason 扩展方法，提供额外的兼容性支持
/// </summary>
public static class ChatFinishReasonExtensions
{
    /// <summary>
    /// 验证指定的 finish_reason 值是否为标准值
    /// </summary>
    public static bool IsStandardFinishReason(string finishReason)
    {
        if (string.IsNullOrEmpty(finishReason))
            return false;

        return finishReason.ToLowerInvariant() switch
        {
            "stop" => true,
            "length" => true,
            "tool_calls" => true,
            "content_filter" => true,
            "function_call" => true,
            _ => false
        };
    }

    /// <summary>
    /// 获取建议的标准 finish_reason 映射
    /// </summary>
    public static string? GetStandardMapping(string finishReason)
    {
        if (string.IsNullOrEmpty(finishReason))
            return null;

        return finishReason.ToLowerInvariant() switch
        {
            "end" => "stop",
            "finished" => "stop",
            "complete" => "stop",
            "done" => "stop",
            "max_tokens" => "length",
            "token_limit" => "length",
            _ => null
        };
    }

    /// <summary>
    /// 检查 JSON 内容中是否包含非标准的 finish_reason 值
    /// </summary>
    public static bool ContainsNonStandardFinishReason(string jsonContent)
    {
        if (string.IsNullOrEmpty(jsonContent))
            return false;

        // 简单的正则表达式检查
        var regex = new System.Text.RegularExpressions.Regex(
            @"""finish_reason""\s*:\s*""([^""]+)""", 
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);

        var matches = regex.Matches(jsonContent);
        
        foreach (System.Text.RegularExpressions.Match match in matches)
        {
            var value = match.Groups[1].Value;
            if (!IsStandardFinishReason(value))
            {
                return true;
            }
        }

        return false;
    }
}
