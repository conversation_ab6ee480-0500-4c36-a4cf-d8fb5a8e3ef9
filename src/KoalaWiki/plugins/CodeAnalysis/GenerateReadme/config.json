{"schema": 1, "type": "completion", "description": "根据仓库目录结构，分析仓库的功能和结构，生成属于仓库的Readme", "execution_settings": {"default": {"max_tokens": 8192, "temperature": 0.5}, "DeepSeek-V3": {"max_tokens": 16384, "temperature": 0.5}, "gpt-4.1-mini": {"max_tokens": 32768, "temperature": 0.5}, "gpt-4.1": {"max_tokens": 32768, "temperature": 0.5}, "gpt-4o": {"max_tokens": 16384, "temperature": 0.5}, "o4-mini": {"max_tokens": 100000, "temperature": 0.5}, "o3-mini": {"max_tokens": 100000, "temperature": 0.5}, "deepseek-chat": {"max_tokens": 8192, "temperature": 0.5}, "gemini-2.5-pro": {"max_tokens": 65536, "temperature": 0.5}, "gemini-2.5-flash": {"max_tokens": 16384, "temperature": 0.5}, "Qwen/Qwen3-235B-A22B": {"max_tokens": 32768, "temperature": 0.5}, "grok-3": {"max_tokens": 65536, "temperature": 0.5}, "qwen3-235b-a22b": {"max_tokens": 16384, "temperature": 0.5}, "qwen2.5-coder-3b-instruct": {"max_tokens": 32768, "temperature": 0.5}}, "input_variables": [{"name": "catalogue", "description": "仓库目录结构", "required": true}, {"name": "branch", "description": "仓库分支", "required": true}, {"name": "git_repository", "description": "仓库地址", "required": true}]}