Analyze Git commit history and generate a structured changelog.

Input data:
- README: {{$readme}}
- Commits: {{$commit_message}}
- Repository: {{$git_repository}}
- Branch: {{$branch}}

Generate a JSON array of changelog entries. Group commits by date (daily for last 7 days, aggregate older commits).

Categorize changes:
- 🆕 New Features
- 🐛 Bug Fixes  
- 🔄 Refactoring
- 📝 Documentation
- 🔧 Configuration
- 📦 Dependencies
- ⚠️ Breaking Changes

Output format:
<changelog>
[
  {
    "date": "2024-01-15T10:30:00",
    "title": "Feature updates and bug fixes",
    "description": "Added new authentication system and resolved API issues"
  }
]
</changelog>

Be concise and focus on user-facing changes. Use project terminology from README.