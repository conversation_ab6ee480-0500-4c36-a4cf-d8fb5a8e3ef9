{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ChatModel": "", "EmbeddingsModel": "", "AnalysisModel": "", "ChatApiKey": "", "Endpoint": "", "EnableDetailedApiLogging": false, "Document": {"ExcludedFolders": ["./.venv/", "./venv/", "./env/", "./virtualenv/", "./node_modules/", "./bower_components/", "./jspm_packages/", "./.git/", "./.svn/", "./.hg/", "./.bzr/", "./__pycache__/", "./.pytest_cache/", "./.mypy_cache/", "./.ruff_cache/", "./.coverage/", "./dist/", "./build/", "./out/", "./target/", "./bin/", "./obj/", "./docs/", "./_docs/", "./site-docs/", "./_site/", "./.idea/", "./.vscode/", "./.vs/", "./.eclipse/", "./.settings/", "./logs/", "./log/", "./tmp/", "./temp/", "./.eng", "./.idea/", "./.vscode/"], "ExcludedFiles": ["package-lock.json", "yarn.lock", "pnpm-lock.yaml", "npm-shrinkwrap.json", "poetry.lock", "Pipfile.lock", "requirements.txt.lock", "Cargo.lock", "composer.lock", ".lock", ".DS_Store", "Thumbs.db", "desktop.ini", "*.lnk", ".env", ".env.*", "*.env", "*.cfg", "*.ini", ".flaskenv", ".giti<PERSON>re", ".gitattributes", ".git<PERSON><PERSON><PERSON>", ".github", ".gitlab-ci.yml", ".prettier<PERSON>", ".eslintrc", ".es<PERSON><PERSON><PERSON>", ".stylelintrc", ".editorconfig", ".j<PERSON>trc", ".pyl<PERSON><PERSON>", ".flake8", "mypy.ini", "pyproject.toml", "tsconfig.json", "webpack.config.js", "babel.config.js", "rollup.config.js", "jest.config.js", "karma.conf.js", "vite.config.js", "next.config.js", "*.min.js", "*.min.css", "*.bundle.js", "*.bundle.css", "*.map", "*.gz", "*.zip", "*.tar", "*.tgz", "*.rar", "*.pyc", "*.pyo", "*.pyd", "*.so", "*.dll", "*.class", "*.exe", "*.o", "*.a", "*.jpg", "*.jpeg", "*.png", "*.gif", "*.ico", "*.svg", "*.webp", "*.mp3", "*.mp4", "*.wav", "*.avi", "*.mov", "*.webm", "*.csv", "*.tsv", "*.xls", "*.xlsx", "*.db", "*.sqlite", "*.sqlite3", "*.pdf", "*.docx", "*.pptx", ".doc", "*.ppt", "*.xls", "*.resw", "*.nupkg", "*.jar", "*.plist", "*.pyc", "*.log"]}, "ConnectionStrings": {"Type": "sqlite3", "Default": "Data Source=/Users/<USER>/project/self/OpenDeepWiki/KoalaWiki.db"}, "Jwt": {"Issuer": "KoalaWiki", "Audience": "KoalaWiki", "Secret": "KoalaWiki-Giasdh&*(YGV%%GR$RFGI(UH*GA*^&%A^&%$GIOBOHNFG)A_)_-9as0djoinoJKGBHGVYGFYT%%%$FFGAO))&%+_", "ExpireMinutes": 144000, "RefreshExpireMinutes": 1008000}, "GitHub": {"ClientId": "", "ClientSecret": "", "Token": ""}, "Google": {"ClientId": ""}}