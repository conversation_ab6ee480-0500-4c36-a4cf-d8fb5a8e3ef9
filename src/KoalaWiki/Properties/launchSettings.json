{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "http://localhost:5085", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "TASK_MAX_SIZE_PER_USER": "3", "ENABLE_WAREHOUSE_DESCRIPTION_TASK": "false", "EnableWarehouseFunctionPromptTask": "false", "ENABLE_INCREMENTAL_UPDATE": "false", "REFINE_AND_ENHANCE_QUALITY": "false", "FeishuBotName": "OpenDeepWiki", "FeishuAppSecret": "GV1EW5RNQGri1TCZl4Lc2cQlo4kaSRiA", "FeishuAppId": "cli_a806079600bdd00d"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "https://localhost:7065;http://localhost:5085", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "Urls": "https://localhost:7065;http://localhost:5085"}}}}