<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>eb1bff0b-6bad-4959-905a-fdfcb9e2a5b3</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost"  />
    <PackageReference Include="Aspire.Hosting.NodeJs" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\KoalaWiki\KoalaWiki.csproj" />
  </ItemGroup>

</Project>
