{"common": {"search": "搜索", "add": "添加", "cancel": "取消", "confirm": "确认", "submit": "提交", "submitting": "提交中...", "save": "保存", "edit": "编辑", "delete": "删除", "loading": "加载中...", "refresh": "刷新", "clear": "清除", "close": "关闭", "back": "返回", "next": "下一页", "prev": "上一页", "total": "共 {{count}} 个", "no_data": "暂无数据", "error": "出错了", "success": "操作成功", "failed": "操作失败", "expand": "展开", "collapse": "收起", "reset": "重置", "viewOnGithub": "在 GitHub 上查看", "upload": "上传", "download": "下载", "restart": "重启", "later": "稍后", "minutes": "分钟", "yes": "是", "no": "否", "configured": "已配置", "notConfigured": "未配置", "optional": "可选", "enabled": "已启用", "disabled": "已禁用", "unknown": "未知", "notSet": "未设置", "more": "更多"}, "nav": {"home": "首页", "repositories": "仓库", "docs": "文档", "about": "关于", "login": "登录", "register": "注册", "profile": "个人资料", "settings": "设置", "admin_console": "管理控制台", "logout": "退出登录"}, "home": {"title": "AI驱动的代码知识库", "subtitle": "AI驱动的代码知识库，支持代码分析、文档生成和知识图谱创建", "search_placeholder": "搜索仓库名称或地址", "add_repository": "添加仓库", "query_last_repo": "查询最新仓库", "repository_list": {"title": "知识库列表", "empty": "暂无仓库数据", "empty_description": "请尝试调整搜索条件或稍后再试", "not_found": "未找到相关仓库", "loading": "正在加载仓库数据..."}, "repository_card": {"recommended": "推荐", "branch": "分支", "created": "创建于", "updated": "更新于", "status": {"pending": "待处理", "processing": "处理中", "completed": "已完成", "failed": "失败"}}, "sponsors": {"title": "合作伙伴", "thanks": "感谢以上合作伙伴对 OpenDeepWiki 项目的支持"}}, "footer": {"copyright": "© {{year}} OpenDeepWiki. All rights reserved.", "privacy": "隐私政策", "terms": "服务条款", "github": "GitHub"}, "login": {"title": "登录 KoalaWiki", "subtitle": "使用您的账户登录", "form": {"username": "用户名/邮箱", "username_placeholder": "请输入用户名或邮箱", "password": "密码", "password_placeholder": "请输入密码", "remember_me": "记住我", "forgot_password": "忘记密码？", "login": "登录", "logging_in": "登录中..."}, "oauth": {"github": "使用 GitHub 登录", "google": "使用 Google 登录"}, "or_use_credentials": "或使用账户密码登录", "no_account": "还没有账户？", "register_now": "立即注册", "validation": {"required_fields": "请输入用户名和密码"}, "messages": {"success": "登录成功", "failed": "登录失败，请检查用户名和密码", "error": "登录过程中发生错误，请重试"}}, "register": {"title": "注册 KoalaWiki", "subtitle": "创建一个新的账户", "form": {"username": "用户名", "username_placeholder": "请输入用户名（至少4个字符）", "email": "邮箱", "email_placeholder": "请输入邮箱地址", "password": "密码", "password_placeholder": "请输入密码（至少8个字符）", "confirm_password": "确认密码", "confirm_password_placeholder": "请再次输入密码", "agreement": "我已阅读并同意", "terms": "用户协议", "privacy": "隐私政策", "register": "注册", "registering": "注册中..."}, "oauth": {"github": "使用 GitHub 注册", "google": "使用 Google 注册"}, "or_use_email": "或者使用邮箱注册", "have_account": "已有账户？", "login_now": "立即登录", "validation": {"username_required": "请输入用户名", "username_min_length": "用户名至少需要4个字符", "email_required": "请输入邮箱地址", "email_invalid": "请输入有效的邮箱地址", "password_required": "请输入密码", "password_min_length": "密码至少需要8个字符", "confirm_password_required": "请确认密码", "passwords_not_match": "两次输入的密码不一致", "agreement_required": "请阅读并同意用户协议和隐私政策"}, "messages": {"success": "注册成功", "failed": "注册失败", "error": "注册过程中发生错误，请重试"}}, "repository": {"form": {"title": "添加仓库", "description": "添加 Git 仓库或上传本地文件来创建知识库", "addRepository": "添加仓库", "gitRepository": "Git 仓库", "fileUpload": "文件上传", "customRepository": "自定义仓库", "repositoryAddress": "仓库地址", "customDescription": "手动指定组织名称和仓库名称，适用于特殊场景", "organizationName": "组织名称", "organizationNamePlaceholder": "例如：OpenDeepWiki", "repositoryName": "仓库名称", "repositoryNamePlaceholder": "例如：KoalaWiki", "customAddressPlaceholder": "输入仓库完整地址", "customBranchPlaceholder": "例如：main 或 master", "branch": "分支", "selectBranch": "选择分支", "enableAuth": "启用认证", "authDescription": "私有仓库需要提供认证信息", "username": "用户名", "usernamePlaceholder": "Git 用户名", "password": "密码/访问令牌", "passwordPlaceholder": "密码或个人访问令牌", "fileUploadDescription": "上传 ZIP、TAR 或 TAR.GZ 格式的文件", "uploadMethod": "上传方式", "urlUpload": "URL 上传", "localFile": "本地文件", "fileUrl": "文件 URL", "selectFile": "选择文件", "selectedFile": "已选择文件", "aiSettings": "AI 配置", "optional": "可选", "model": "模型", "apiKey": "API Key", "endpoint": "API 端点", "customPrompt": "自定义提示词", "promptPlaceholder": "输入自定义提示词（可选）", "submitSuccess": "仓库提交成功", "submitFailed": "仓库提交失败", "submitError": "提交失败", "branchPlaceholder": "输入分支名称或从下拉列表选择", "refreshBranches": "刷新分支列表", "branchHelperText": "您可以从下拉列表中选择分支，或手动输入其他分支名称", "availableBranches": "可用分支", "fetchBranchesFailed": "获取分支列表失败", "fetchBranchesError": "获取分支失败", "default": "默认", "email": "邮箱", "emailPlaceholder": "Git 邮箱地址", "validation": {"addressRequired": "请输入仓库地址", "branchRequired": "请选择分支", "fileRequired": "请选择上传文件", "fileUrlRequired": "请输入文件URL", "organizationNameRequired": "请输入组织名称", "repositoryNameRequired": "请输入仓库名称"}}, "nav": {"overview": "概览", "documents": "文档", "fileTree": "文件树", "mindmap": "知识图谱", "changelog": "更新日志", "settings": "设置"}, "layout": {"branch": "分支", "selectBranch": "选择分支", "documentTree": "文档目录", "noDocuments": "暂无文档", "fetchBranchesError": "获取分支列表失败", "fetchDocumentsError": "获取文档目录失败", "loadingDocuments": "正在加载文档...", "searchDocuments": "搜索文档", "expandAll": "全部展开", "collapseAll": "全部收起", "collapseSidebar": "收起侧边栏", "expandSidebar": "展开侧边栏", "introduction": "介绍", "repositoryNotFound": "仓库不存在", "loadingRepository": "加载仓库中...", "branchSwitcher": "切换分支", "checkRepositoryAddress": "请检查仓库地址是否正确", "backToHome": "返回首页", "overview": "概览", "mindMap": "思维导图", "fileTree": "文件树", "codeSearch": "代码搜索", "mindMapDescription": "可视化展示仓库的知识结构和文档关系", "loadingMindMap": "正在加载思维导图...", "mindMapLoadFailed": "思维导图加载失败", "noMindMapData": "暂无思维导图数据", "mindMapGenerating": "思维导图可能正在生成中，请稍后刷新", "mindMapControls": "控制面板", "searchNodes": "搜索节点", "totalNodes": "总节点数", "visibleNodes": "可见节点", "zoomLevel": "缩放级别", "zoomIn": "放大", "zoomOut": "缩小", "resetZoom": "重置缩放", "resetView": "重置视图", "exportSvg": "导出 SVG", "nodeList": "节点列表", "noSearchResults": "无搜索结果", "showingFirst50": "显示前 50 个，共 {{total}} 个节点", "fitToScreen": "适应屏幕", "loginRequired": "请先登录后再下载", "downloadError": "获取仓库信息失败", "downloadSuccess": "下载成功", "downloadFailed": "下载失败", "downloadTooltip": "导出Markdown文件为ZIP压缩包"}, "document": {"relevantSourceFiles": "相关源文件", "showingSourceFiles": "显示 {{count}} 个源文件", "tableOfContents": "目录", "showToc": "显示目录", "hideToc": "隐藏目录", "manualTocMode": "手动目录模式", "manualTocDescription": "此文档内容较少，目录已手动启用。您可以随时关闭它。", "loadFailed": "加载文档失败", "notFound": "文档不存在"}, "detail": {"cloneCommand": "克隆命令", "statistics": "统计信息", "statisticsDescription": "仓库的统计数据概览", "documents": "文档数", "commits": "提交数", "contributors": "贡献者", "stars": "星标数", "loadFailed": "加载仓库信息失败", "notFound": "仓库不存在", "backToHome": "返回首页", "export": "导出", "tabs": {"overview": "概览", "documents": "文档", "mindmap": "知识图谱"}, "basicInfo": "基本信息", "organization": "组织", "repositoryName": "仓库名称", "status": "状态", "gitInfo": "Git 信息", "repositoryUrl": "仓库地址", "branch": "分支", "createdAt": "创建时间", "updatedAt": "更新时间", "documentList": "文档列表", "documentListDescription": "浏览和管理仓库中的文档", "documentsComingSoon": "文档功能即将上线...", "knowledgeGraph": "知识图谱", "knowledgeGraphDescription": "可视化展示仓库的知识结构", "mindmapComingSoon": "知识图谱功能即将上线..."}}, "settings": {"title": "系统设置", "description": "配置和管理系统各项功能设置", "loadFailed": "加载设置失败", "saveSuccess": "设置保存成功", "saveFailed": "设置保存失败", "saveAll": "保存所有设置", "resetGroup": "重置分组", "resetGroupConfirm": "确定要重置当前分组的所有设置为默认值吗？", "resetSuccess": "重置成功", "resetFailed": "重置失败", "clearCache": "清空缓存", "cacheCleared": "缓存已清空", "cacheClearFailed": "清空缓存失败", "unsavedChanges": "有未保存的更改", "restartRequired": "需要重启", "restartRequiredMessage": "某些设置更改需要重启系统才能生效", "restartConfirm": "确认重启系统", "restartConfirmMessage": "系统重启可能需要几分钟时间，确定要继续吗？", "restartInitiated": "系统重启已启动", "restartFailed": "系统重启失败", "requiresRestart": "需要重启", "importExport": {"title": "导入导出", "export": "导出设置", "import": "导入设置", "exportNote": "导出说明", "exportNoteDescription": "导出的设置文件不包含敏感信息如密码和API密钥", "importNote": "导入警告", "importNoteDescription": "导入设置会覆盖现有配置，请确保备份重要设置", "exportSettings": "导出设置", "selectGroups": "选择分组", "selectGroupsDescription": "选择要导出的设置分组，留空表示导出所有", "selectGroupsPlaceholder": "选择设置分组", "downloadSettings": "下载设置文件", "exportSuccess": "设置导出成功", "exportFailed": "设置导出失败", "uploadFile": "上传文件", "dragFileHere": "点击或拖拽文件到此处上传", "supportedFormats": "支持JSON格式的设置文件", "importOptions": "导入选项", "selectImportGroups": "选择导入分组", "selectImportGroupsDescription": "选择要导入的设置分组", "overwriteExisting": "覆盖现有设置", "overwriteExistingDescription": "是否覆盖已存在的设置项", "previewSettings": "预览设置 ({{count}}项)", "key": "键名", "group": "分组", "description": "描述", "sensitive": "敏感", "fileLoaded": "文件加载成功，共{{count}}项设置", "invalidFile": "无效的设置文件格式", "selectFileFirst": "请先选择要导入的文件", "importSuccess": "设置导入成功", "importFailed": "设置导入失败", "importInProgress": "正在导入设置...", "importComplete": "导入完成", "importSettings": "导入设置", "securityWarning": "安全警告", "securityTip1": "只导入来源可信的设置文件", "securityTip2": "导入前请仔细检查设置内容", "securityTip3": "敏感信息不会被导出，需要重新配置", "securityTip4": "导入后建议重启系统以确保配置生效"}, "validationFailed": "配置验证失败", "noSettingsAvailable": "暂无可用设置", "groups": {"basic": "基本设置", "email": "邮件配置", "ai": "AI设置", "storage": "存储设置", "security": "安全设置", "github": "GitHub集成", "gitee": "Gitee集成", "document": "文档配置", "jwt": "JWT配置", "backup": "备份恢复", "logging": "日志设置"}, "basicDescription": "配置网站的基本信息，包括名称、Logo、描述等", "emailDescription": "配置SMTP邮件服务器，用于发送系统通知邮件", "aiDescription": "配置AI模型和API设置，用于代码分析和文档生成", "storageDescription": "配置文件存储、上传限制和云存储等设置", "securityDescription": "配置密码策略、会话管理、IP访问控制等安全设置", "documentDescription": "配置文档处理、代码分析和仓库处理相关设置", "thirdPartyDescription": "配置{{platform}}OAuth集成和API访问设置", "basic": {"siteName": "站点名称", "siteNamePlaceholder": "请输入站点名称", "siteDescription": "站点描述", "siteDescriptionPlaceholder": "请输入站点描述", "siteLogo": "站点Logo", "siteLogoPlaceholder": "请输入Logo URL或上传图片", "logoUrl": "Logo URL", "favicon": "网站图标", "faviconPlaceholder": "请输入Favicon URL", "copyright": "版权信息", "copyrightPlaceholder": "© 2024 Your Company", "keywords": "关键词", "keywordsPlaceholder": "用逗号分隔关键词", "contactEmail": "联系邮箱", "contactEmailPlaceholder": "<EMAIL>", "supportUrl": "支持URL", "supportUrlPlaceholder": "https://support.example.com", "privacyPolicyUrl": "隐私政策URL", "privacyPolicyUrlPlaceholder": "https://example.com/privacy", "termsOfServiceUrl": "服务条款URL", "termsOfServiceUrlPlaceholder": "https://example.com/terms", "analyticsCode": "统计代码", "analyticsCodePlaceholder": "输入Google Analytics或其他统计代码", "analyticsCodeHelp": "支持Google Analytics、百度统计等第三方统计代码"}, "email": {"testConnection": "测试连接", "testSuccess": "邮件发送测试成功", "testFailed": "邮件发送测试失败", "testEmailRequired": "请输入测试邮箱地址", "securityNote": "安全提示", "securityNoteDescription": "邮件密码等敏感信息会被加密存储，请确保SMTP服务器配置正确", "smtpHost": "SMTP服务器", "smtpHostPlaceholder": "smtp.example.com", "smtpPort": "SMTP端口", "smtpPortPlaceholder": "587", "smtpUser": "SMTP用户名", "smtpUserPlaceholder": "<EMAIL>", "smtpPassword": "SMTP密码", "smtpPasswordPlaceholder": "请输入SMTP密码", "securitySettings": "安全设置", "enableSsl": "启用SSL", "enableSslHelp": "通常用于465端口", "enableTls": "启用TLS", "enableTlsHelp": "通常用于587端口", "senderInfo": "发件人信息", "senderName": "发件人名称", "senderNamePlaceholder": "系统通知", "senderEmail": "发件人邮箱", "senderEmailPlaceholder": "<EMAIL>", "replyToEmail": "回复邮箱", "replyToEmailPlaceholder": "<EMAIL>", "maxEmailsPerHour": "每小时最大发送数", "maxEmailsPerHourHelp": "限制系统每小时发送的邮件数量", "emailTemplate": "邮件模板", "emailTemplatePlaceholder": "HTML邮件模板，支持变量替换", "emailTemplateHelp": "支持HTML格式，可使用{{变量名}}进行替换", "testEmailSettings": "测试邮件设置", "testEmailAddress": "测试邮箱地址", "testEmailPlaceholder": "<EMAIL>", "testSubject": "邮件主题", "defaultTestSubject": "系统邮件测试", "testBody": "邮件内容", "defaultTestBody": "这是一封测试邮件，用于验证邮件配置是否正确。", "sendTestEmail": "发送测试邮件", "testNote": "测试说明", "testNoteDescription": "测试邮件将使用当前配置发送到指定邮箱，请确保配置信息正确"}, "ai": {"testConnection": "测试连接", "testSuccess": "AI API连接测试成功", "testFailed": "AI API连接测试失败", "testRequiredFields": "请先配置API端点和密钥", "apiKeyNote": "API密钥安全提示", "apiKeyNoteDescription": "API密钥是敏感信息，会被加密存储，请妥善保管", "modelProvider": "模型提供商", "modelProviderPlaceholder": "选择AI模型提供商", "customProvider": "自定义提供商", "endpoint": "API端点", "endpointPlaceholder": "https://api.openai.com/v1", "endpointHelp": "AI服务的API基础URL", "apiKey": "API密钥", "apiKeyPlaceholder": "sk-...", "modelConfiguration": "模型配置", "chatModel": "聊天模型", "chatModelPlaceholder": "gpt-4", "analysisModel": "分析模型", "analysisModelPlaceholder": "gpt-4", "deepResearchModel": "深度研究模型", "deepResearchModelPlaceholder": "gpt-4", "maxFileLimit": "最大文件限制", "modelParameters": "模型参数", "temperature": "温度", "temperatureHelp": "控制输出的随机性，0.0-2.0", "topP": "Top P", "topPHelp": "核采样参数，0.0-1.0", "maxTokens": "最大令牌数", "frequencyPenalty": "频率惩罚", "frequencyPenaltyHelp": "减少重复内容，-2.0到2.0", "presencePenalty": "存在惩罚", "presencePenaltyHelp": "鼓励谈论新话题，-2.0到2.0", "mem0Configuration": "Mem0配置", "mem0Help": "Mem0是一个记忆管理系统，可以帮助AI记住之前的对话", "enableMem0": "启用Mem0", "mem0ApiKey": "Mem0 API密钥", "mem0ApiKeyPlaceholder": "mem0-...", "mem0Endpoint": "Mem0端点", "mem0EndpointPlaceholder": "https://api.mem0.ai", "testAPIConnection": "测试API连接", "testDescription": "测试当前配置是否能正常连接到AI服务", "currentConfiguration": "当前配置", "model": "模型", "testNote": "测试说明", "testNoteDescription": "测试将发送一个简单的请求到AI服务，验证连接和认证是否正常"}, "storage": {"localStorage": "本地存储", "fileStoragePath": "文件存储路径", "fileStoragePathPlaceholder": "/var/lib/koalawiki/files", "fileStoragePathHelp": "确保路径存在且具有读写权限", "backupStoragePath": "备份存储路径", "backupStoragePathPlaceholder": "/var/lib/koalawiki/backups", "uploadLimits": "上传限制", "maxFileSize": "最大文件大小", "maxFileSizeHelp": "单个文件的最大大小限制", "allowedFileTypes": "允许的文件类型", "allowedFileTypesHelp": "允许上传的文件扩展名", "allowedFileTypesPlaceholder": "选择允许的文件类型", "compression": "文件压缩", "enableCompression": "启用压缩", "compressionQuality": "压缩质量", "compressionQualityHelp": "图片压缩质量，1-100", "storageTiers": "存储层级", "defaultStorageTier": "默认存储层级", "storageTiersHelp": "选择文件的默认存储层级", "storageTiersPlaceholder": "选择存储层级", "standard": "标准存储", "cold": "冷存储", "archive": "归档存储", "cloudStorage": "云存储", "enableCloudStorage": "启用云存储", "cloudStorageProvider": "云存储提供商", "cloudStorageProviderPlaceholder": "选择云存储提供商", "local": "本地存储", "aliyunOSS": "阿里云OSS", "tencentCOS": "腾讯云COS", "custom": "自定义", "cloudStorageConfig": "云存储配置", "cloudStorageConfigPlaceholder": "JSON格式的云存储配置", "cloudStorageConfigHelp": "根据选择的提供商输入相应的配置信息", "storageUsage": "存储使用情况", "storageUsageDescription": "当前系统的存储使用统计", "totalSpace": "总空间", "usedSpace": "已使用", "freeSpace": "可用空间", "optimizationTips": "存储优化建议", "tip1": "定期清理过期的临时文件和日志", "tip2": "对大文件启用压缩以节省空间", "tip3": "将不常访问的文件迁移到冷存储", "tip4": "设置合理的文件上传大小限制"}, "security": {"securityNote": "安全警告", "securityNoteDescription": "安全设置会影响所有用户的访问，请谨慎修改", "passwordPolicy": "密码策略", "passwordMinLength": "最小密码长度", "requireNumbers": "需要数字", "requireSymbols": "需要特殊字符", "requireUppercase": "需要大写字母", "requireLowercase": "需要小写字母", "sessionManagement": "会话管理", "sessionTimeout": "会话超时", "sessionTimeoutHelp": "用户无操作后的自动退出时间", "maxLoginAttempts": "最大登录尝试次数", "lockoutDuration": "锁定时长", "twoFactorAuth": "双因素认证", "enableTwoFactorAuth": "启用双因素认证", "twoFactorAuthHelp": "为用户账户增加额外的安全层", "twoFactorAuthEnabled": "双因素认证已启用", "twoFactorAuthEnabledDescription": "用户可以在个人设置中配置双因素认证", "ipAccessControl": "IP访问控制", "allowedIpAddresses": "允许的IP地址", "allowedIpHelp": "只有这些IP地址可以访问系统", "allowedIpDescription": "留空表示允许所有IP访问", "blockedIpAddresses": "禁止的IP地址", "blockedIpHelp": "这些IP地址将被禁止访问", "blockedIpDescription": "黑名单优先级高于白名单", "ipAddressPlaceholder": "*********** 或 ***********/24", "captchaSettings": "验证码设置", "enableCaptcha": "启用验证码", "captchaProvider": "验证码提供商", "captchaProviderPlaceholder": "选择验证码服务", "customCaptcha": "自定义验证码", "captchaConfig": "验证码配置", "captchaConfigPlaceholder": "JSON格式的验证码配置", "captchaConfigHelp": "根据选择的提供商输入相应的配置信息", "securityTips": "安全建议", "tip1": "定期更新系统和依赖包", "tip2": "启用HTTPS和安全头", "tip3": "配置防火墙和入侵检测", "tip4": "定期备份重要数据", "tip5": "监控系统日志和异常活动"}, "thirdParty": {"setupNote": "设置说明", "setupNoteDescription": "在使用{{platform}}集成之前，您需要在{{platform}}上创建一个OAuth应用", "createApp": "创建应用", "viewDocs": "查看文档", "oauthConfiguration": "OAuth配置", "inputPlaceholder": "请输入{{field}}", "oauthSettings": "OAuth设置", "enableOAuth": "启用OAuth登录", "enableOAuthHelp": "允许用户使用{{platform}}账户登录", "allowedOrganizations": "允许的组织", "allowedOrganizationsHelp": "限制只有这些组织的成员可以登录", "allowedOrganizationsDescription": "留空表示允许所有用户", "allowedOrganizationsPlaceholder": "输入组织名称", "repositorySync": "仓库同步", "enableAutoSync": "启用自动同步", "enableAutoSyncHelp": "定期同步仓库信息和权限", "syncInterval": "同步间隔", "syncIntervalPlaceholder": "选择同步频率", "every5Minutes": "每5分钟", "every15Minutes": "每15分钟", "every30Minutes": "每30分钟", "everyHour": "每小时", "every6Hours": "每6小时", "every12Hours": "每12小时", "everyDay": "每天", "defaultRepository": "默认仓库", "defaultRepositoryHelp": "新用户的默认仓库访问权限", "defaultRepositoryPlaceholder": "owner/repository", "callbackUrls": "回调URL", "callbackUrlsDescription": "在OAuth应用设置中需要配置以下回调URL", "connectionStatus": "连接状态", "oauthStatus": "OAuth状态", "apiTokenStatus": "API令牌状态", "autoSyncStatus": "自动同步状态", "configurationGuide": "配置指南", "step1": "在{{platform}}上创建OAuth应用", "step2": "复制Client ID和Client Secret", "step3": "配置回调URL和权限", "step4": "在此处输入配置信息", "step5": "测试连接并启用功能"}, "github": {"clientId": "GitHub Client ID", "clientSecret": "GitHub Client Secret", "token": "GitHub Access Token", "webhookSecret": "Webhook Secret"}, "gitee": {"clientId": "Gitee Client ID", "clientSecret": "Gitee <PERSON>lient <PERSON>", "token": "Gitee Access Token", "webhookSecret": "Webhook Secret"}, "document": {"basicSettings": "基本设置", "enableIncrementalUpdate": "启用增量更新", "enableIncrementalUpdateHelp": "只处理变更的文件，提高处理效率", "catalogueFormat": "目录格式", "catalogueFormatPlaceholder": "选择目录结构格式", "formatCompact": "紧凑格式", "formatDetailed": "详细格式", "formatTree": "树形格式", "formatFlat": "平铺格式", "maxFileReadCount": "最大文件读取数", "maxFileReadCountHelp": "单次处理的最大文件数量", "defaultLanguage": "默认语言", "defaultLanguageHelp": "文档和代码的默认编程语言", "defaultLanguagePlaceholder": "选择默认编程语言", "fileFiltering": "文件过滤", "enableSmartFilter": "启用智能过滤", "enableSmartFilterHelp": "AI智能识别并过滤无关文件", "smartFilterNote": "智能过滤可以自动识别配置文件、依赖文件等无需处理的文件", "excludedFiles": "排除文件", "excludedFilesHelp": "这些文件将被跳过处理", "excludedFilesDescription": "支持文件名和扩展名匹配", "excludedFilesPlaceholder": "*.log", "excludedFolders": "排除文件夹", "excludedFoldersHelp": "这些文件夹将被跳过处理", "excludedFoldersDescription": "支持相对路径和绝对路径", "excludedFoldersPlaceholder": "node_modules", "codeAnalysis": "代码分析", "enableCodeDependencyAnalysis": "启用代码依赖分析", "enableCodeDependencyAnalysisHelp": "分析代码文件之间的依赖关系", "enableCodeCompression": "启用代码压缩", "enableCodeCompressionHelp": "压缩代码内容以节省存储空间", "supportedLanguages": "支持的编程语言", "supportedLanguagesHelp": "选择需要处理的编程语言", "supportedLanguagesPlaceholder": "选择编程语言", "warehouseTasks": "仓库处理任务", "enableWarehouseFunctionPromptTask": "启用仓库功能提示任务", "enableWarehouseFunctionPromptTaskHelp": "为仓库生成功能描述和使用提示", "enableWarehouseDescriptionTask": "启用仓库描述任务", "enableWarehouseDescriptionTaskHelp": "自动生成仓库的详细描述", "enableFileCommit": "启用文件提交", "enableFileCommitHelp": "处理完成后自动提交文件更改", "enableWarehouseCommit": "启用仓库提交", "enableWarehouseCommitHelp": "处理完成后自动提交仓库更改", "refineAndEnhanceQuality": "精炼并提高质量", "refineAndEnhanceQualityHelp": "使用AI优化文档质量和可读性", "processingStats": "处理统计", "totalRepositories": "总仓库数", "processedFiles": "已处理文件", "avgProcessingTime": "平均处理时间", "successRate": "成功率", "optimizationTips": "处理优化建议", "tip1": "启用智能过滤可以显著提高处理速度", "tip2": "合理设置排除文件和文件夹规则", "readMaxTokens": "最大读取令牌数", "readMaxTokensHelp": "单次AI请求的最大令牌数限制（1000-200000）", "proxy": "Git代理", "proxyHelp": "Git操作的HTTP/HTTPS代理，格式：http://代理服务器:端口", "proxyPlaceholder": "http://proxy.example.com:8080", "enableAgentTool": "启用Agent工具", "enableAgentToolHelp": "启用Agent工具插件以增强功能，默认提供深入思考规划", "tip3": "根据仓库大小和AI模型限制调整最大读取令牌数", "tip4": "定期清理处理失败的任务", "tip5": "监控AI模型的使用配额"}, "systemStatus": {"title": "系统状态", "restartRequiredCount": "{{count}}项需要重启", "version": "版本", "environment": "环境", "uptime": "运行时间", "activeConnections": "活跃连接", "performance": "性能指标", "cpuUsage": "CPU使用率", "memoryUsage": "内存使用率", "diskUsage": "磁盘使用率", "features": "功能模块", "emailService": "邮件服务", "aiService": "AI服务", "backupService": "备份服务", "securityService": "安全服务", "restartRequired": "需要重启", "restartRequiredDescription": "{{count}}项设置更改需要重启系统", "healthScore": "系统健康度", "healthExcellent": "优秀", "healthGood": "良好", "healthFair": "一般", "healthPoor": "较差"}}, "messages": {"network_error": "网络错误，请检查网络连接", "server_error": "服务器错误，请稍后重试", "unknown_error": "未知错误", "operation_success": "操作成功", "operation_failed": "操作失败"}, "admin": {"title": "管理控制台", "subtitle": "管理用户、角色、仓库和系统设置", "nav": {"dashboard": "仪表板", "users": "用户管理", "roles": "角色管理", "repositories": "仓库管理", "settings": "系统设置"}, "dashboard": {"title": "仪表板", "overview": "系统概览", "welcome": "欢迎回来", "stats": {"total_users": "用户总数", "total_repos": "仓库总数", "total_roles": "角色总数", "total_documents": "文档总数", "active_sessions": "活跃会话", "online_users": "在线用户", "monthly_growth": "月增长率", "daily_active": "今日活跃", "weekly_active": "本周活跃", "monthly_active": "本月活跃", "total_views": "总访问量", "monthly_new_users": "本月新增用户", "monthly_new_repos": "本月新增仓库", "monthly_new_docs": "本月新增文档"}, "charts": {"user_growth": "用户增长", "repo_activity": "仓库活动", "role_distribution": "角色分布", "performance_trends": "性能趋势", "system_health": "系统健康度"}, "performance": {"title": "系统性能监控", "cpu_usage": "CPU使用率", "memory_usage": "内存使用率", "disk_usage": "磁盘使用率", "network_usage": "网络使用率", "active_connections": "活跃连接", "uptime": "运行时间", "system_load": "系统负载", "response_time": "响应时间"}, "health": {"title": "服务健康状态", "overall_score": "总体评分", "health_level": "健康度等级", "excellent": "优秀", "good": "良好", "fair": "一般", "poor": "较差", "database": "数据库", "ai_service": "AI服务", "email_service": "邮件服务", "file_storage": "文件存储", "system_performance": "系统性能", "status_healthy": "健康", "status_warning": "警告", "status_error": "错误", "warnings": "系统警告", "errors": "系统错误"}, "activity": {"title": "最近活动", "recent_users": "最近注册的用户", "recent_repos": "最近创建的仓库", "recent_errors": "最近错误日志", "popular_content": "热门内容", "user_activity": "用户活跃度统计", "no_description": "暂无描述", "online": "在线", "offline": "离线", "growth_rate": "增长率", "document_count": "文档数"}, "status": {"completed": "已完成", "processing": "处理中", "pending": "待处理", "failed": "失败"}, "actions": {"title": "快速操作", "description": "常用的管理操作", "add_user": "添加用户", "add_repository": "添加仓库", "manage_roles": "管理角色", "system_settings": "系统设置", "view_reports": "查看报表", "view_logs": "查看日志", "backup_data": "备份数据", "refresh": "刷新", "auto_refresh": "自动刷新", "last_updated": "最后更新", "retry": "重试"}, "errors": {"load_failed": "数据加载失败", "load_failed_desc": "无法获取仪表板数据，请检查网络连接或稍后重试", "network_error": "网络错误", "server_error": "服务器错误", "timeout_error": "请求超时"}, "trends": {"user_trends": "用户趋势", "repository_trends": "仓库趋势", "document_trends": "文档趋势", "view_trends": "访问量趋势", "performance_trends_24h": "性能趋势（24小时）", "past_30_days": "过去30天", "past_24_hours": "过去24小时", "compared_to_last_month": "相比上月", "compared_to_last_week": "相比上周"}}, "users": {"title": "用户管理", "subtitle": "管理系统用户及其信息", "create": "创建用户", "edit": "编辑用户", "delete": "删除用户", "assign_roles": "分配角色", "search_placeholder": "按用户名或邮箱搜索", "table": {"username": "用户名", "email": "邮箱", "role": "角色", "status": "状态", "created_at": "创建时间", "updated_at": "更新时间", "last_login": "最后登录", "actions": "操作"}, "form": {"username": "用户名", "email": "邮箱", "password": "密码", "confirm_password": "确认密码", "role": "角色", "avatar": "头像"}, "dialogs": {"create_title": "创建用户", "create_description": "创建新用户", "edit_title": "编辑用户", "edit_description": "编辑用户信息", "reset_password_title": "重置密码", "reset_password_description": "为用户设置新的登录密码", "assign_roles_title": "分配角色", "assign_roles_description": "为用户分配角色和权限", "batch_delete_title": "批量删除用户", "batch_delete_description": "确认删除选中的用户？此操作不可撤销"}, "actions": {"reset_password": "重置密码", "assign_roles": "分配角色", "batch_delete": "批量删除", "select_all": "全选", "cancel_selection": "取消选择"}, "filters": {"all_roles": "所有角色", "filter_by_role": "按角色筛选"}, "status": {"active": "活跃", "inactive": "非活跃", "suspended": "已暂停"}}, "roles": {"title": "角色管理", "subtitle": "管理角色和权限", "create": "创建角色", "edit": "编辑角色", "delete": "删除角色", "permissions": "权限", "search_placeholder": "按角色名称搜索", "table": {"name": "角色名称", "description": "描述", "users_count": "用户数", "permissions_count": "权限数", "status": "状态", "created_at": "创建时间", "actions": "操作"}, "form": {"name": "角色名称", "description": "描述", "status": "状态"}}, "repositories": {"title": "仓库管理", "subtitle": "管理仓库和内容", "search_placeholder": "按仓库名称搜索", "table": {"name": "仓库名称", "organization": "组织", "status": "状态", "documents": "文档数", "created_at": "创建时间", "updated_at": "更新时间", "actions": "操作"}, "detail": {"title": "仓库详情", "tree": "内容树", "editor": "内容编辑器", "properties": "属性", "permissions": "权限", "overview": "概览", "documents": "文档管理", "sync": "同步管理", "config": "配置管理", "tasks": "任务管理", "logs": "操作日志", "files": "文件浏览", "basic_info": "基本信息", "repository_name": "仓库名称", "organization_name": "组织名称", "repository_address": "仓库地址", "branch": "分支", "repository_type": "仓库类型", "is_recommended": "是否推荐", "description": "描述", "no_description": "暂无描述", "created_at": "创建于", "updated_at": "更新于", "processing_progress": "处理进度", "document_generation_progress": "文档生成进度", "processing_estimated_time": "正在处理文档，预计还需要5分钟...", "statistics": "统计信息", "total_documents": "文档总数", "total_files": "文件总数", "completed_documents": "已完成文档", "pending_documents": "待处理文档", "last_sync_time": "最后同步时间", "never_synced": "未同步", "file_type_distribution": "文件类型分布", "document_directory": "文档目录", "browse_document_structure": "浏览和管理文档结构", "search_documents": "搜索文档...", "document_content": "文档内容", "view_edit_document_content": "查看和编辑文档内容", "select_document_to_view": "选择一个文档来查看其内容", "sync_management": "同步管理", "manage_sync_settings_history": "管理仓库的同步设置和历史", "manual_sync": "手动同步", "manual_sync_repository": "手动同步仓库", "sync_description": "这将从源仓库拉取最新的文件并重新生成文档。", "start_sync": "开始同步", "sync_status": "同步状态", "auto_sync": "自动同步", "enable": "启用", "sync_history": "同步历史", "files_changed": "个文件", "no_sync_history": "暂无同步历史", "permission_management": "权限管理", "manage_repository_permissions": "管理用户和角色对此仓库的访问权限", "permission_development": "权限管理功能开发中...", "config_management": "配置管理", "manage_repository_config": "管理仓库的各种配置选项", "config_development": "配置管理功能开发中...", "task_management": "任务管理", "view_manage_repository_tasks": "查看和管理仓库相关的处理任务", "task_development": "任务管理功能开发中...", "operation_logs": "操作日志", "view_repository_operation_history": "查看仓库的操作历史记录", "no_operation_logs": "暂无操作日志", "has_error": "有错误", "file_browser": "文件浏览", "browse_manage_repository_content": "浏览和管理仓库内容", "search_files": "搜索文件...", "select_file_to_edit": "选择一个文件来编辑其内容", "file_content": "文件内容", "saving": "保存中...", "save": "保存", "modified_at": "修改于", "export": "导出", "edit": "编辑", "delete": "删除", "confirm_delete_repository": "确认删除仓库", "delete_repository_warning": "此操作将永久删除仓库及其所有数据，此操作不可撤销。", "cancel": "取消", "confirm_delete": "确认删除", "refresh": "刷新", "refreshing": "刷新中...", "file_save_success": "文件保存成功", "file_save_failed": "文件保存失败", "repository_refresh_success": "仓库刷新成功", "repository_refresh_failed": "仓库刷新失败", "repository_delete_success": "仓库删除成功", "repository_delete_failed": "仓库删除失败", "sync_task_started": "同步任务已启动", "sync_start_failed": "同步启动失败", "export_success": "导出成功", "export_failed": "导出失败", "load_repository_data_failed": "加载仓库数据失败", "repository_not_found": "仓库不存在", "return_to_repository_list": "返回仓库列表"}}, "common": {"completed": "已完成", "processing": "处理中", "pending": "待处理", "failed": "失败", "save": "保存", "cancel": "取消", "delete": "删除", "edit": "编辑", "create": "创建", "search": "搜索", "filter": "筛选", "export": "导出", "import": "导入", "refresh": "刷新", "back": "返回", "confirm": "确认", "yes": "是", "no": "否"}, "messages": {"create_success": "创建成功", "update_success": "更新成功", "delete_success": "删除成功", "delete_confirm": "确定要删除此项吗？", "operation_failed": "操作失败", "no_permission": "您没有权限访问此页面", "loading": "加载中...", "no_data": "暂无数据"}}}