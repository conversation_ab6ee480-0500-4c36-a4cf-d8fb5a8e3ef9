{"common": {"search": "Search", "add": "Add", "cancel": "Cancel", "confirm": "Confirm", "submit": "Submit", "submitting": "Submitting...", "save": "Save", "edit": "Edit", "delete": "Delete", "loading": "Loading...", "refresh": "Refresh", "clear": "Clear", "close": "Close", "back": "Back", "next": "Next", "prev": "Previous", "total": "Total {{count}} items", "no_data": "No data", "error": "Error", "success": "Success", "failed": "Failed", "expand": "Expand", "collapse": "Collapse", "reset": "Reset", "viewOnGithub": "View on GitHub"}, "nav": {"home": "Home", "repositories": "Repositories", "docs": "Docs", "about": "About", "login": "<PERSON><PERSON>", "register": "Register", "profile": "Profile", "settings": "Settings", "admin_console": "<PERSON><PERSON>", "logout": "Logout"}, "home": {"title": "AI-Driven Code Knowledge Base", "subtitle": "AI-driven code knowledge base supporting code analysis, document generation, and knowledge graph creation", "search_placeholder": "Search repository name or address", "add_repository": "Add Repository", "query_last_repo": "Query Latest Repository", "repository_list": {"title": "Repository List", "empty": "No repository data", "empty_description": "Please try adjusting search criteria or try again later", "not_found": "No related repositories found", "loading": "Loading repository data..."}, "repository_card": {"recommended": "Recommended", "branch": "Branch", "created": "Created", "updated": "Updated", "status": {"pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed"}}, "sponsors": {"title": "Partners", "thanks": "Thanks to the above partners for supporting the OpenDeepWiki project"}}, "footer": {"copyright": "© {{year}} OpenDeepWiki. All rights reserved.", "privacy": "Privacy Policy", "terms": "Terms of Service", "github": "GitHub"}, "login": {"title": "Login to KoalaWiki", "subtitle": "Sign in with your account", "form": {"username": "Username/Email", "username_placeholder": "Enter username or email", "password": "Password", "password_placeholder": "Enter password", "remember_me": "Remember me", "forgot_password": "Forgot password?", "login": "<PERSON><PERSON>", "logging_in": "Logging in..."}, "oauth": {"github": "Login with GitHub", "google": "Login with Google"}, "or_use_credentials": "Or use credentials", "no_account": "Don't have an account?", "register_now": "Register now", "validation": {"required_fields": "Please enter username and password"}, "messages": {"success": "Login successful", "failed": "<PERSON><PERSON> failed, please check username and password", "error": "An error occurred during login, please try again"}}, "register": {"title": "Register for KoalaWiki", "subtitle": "Create a new account", "form": {"username": "Username", "username_placeholder": "Enter username (at least 4 characters)", "email": "Email", "email_placeholder": "Enter email address", "password": "Password", "password_placeholder": "Enter password (at least 8 characters)", "confirm_password": "Confirm Password", "confirm_password_placeholder": "Enter password again", "agreement": "I have read and agree to the", "terms": "Terms of Service", "privacy": "Privacy Policy", "register": "Register", "registering": "Registering..."}, "oauth": {"github": "Register with GitHub", "google": "Register with Google"}, "or_use_email": "Or register with email", "have_account": "Already have an account?", "login_now": "Login now", "validation": {"username_required": "Please enter username", "username_min_length": "Username must be at least 4 characters", "email_required": "Please enter email address", "email_invalid": "Please enter a valid email address", "password_required": "Please enter password", "password_min_length": "Password must be at least 8 characters", "confirm_password_required": "Please confirm password", "passwords_not_match": "Passwords do not match", "agreement_required": "Please read and agree to the Terms of Service and Privacy Policy"}, "messages": {"success": "Registration successful", "failed": "Registration failed", "error": "An error occurred during registration, please try again"}}, "repository": {"form": {"title": "Add Repository", "description": "Add a Git repository or upload local files to create a knowledge base", "addRepository": "Add Repository", "gitRepository": "Git Repository", "fileUpload": "File Upload", "customRepository": "Custom Repository", "repositoryAddress": "Repository Address", "customDescription": "Manually specify organization and repository names for special scenarios", "organizationName": "Organization Name", "organizationNamePlaceholder": "e.g., OpenDeepWiki", "repositoryName": "Repository Name", "repositoryNamePlaceholder": "e.g., KoalaWiki", "customAddressPlaceholder": "Enter full repository address", "customBranchPlaceholder": "e.g., main or master", "branch": "Branch", "selectBranch": "Select Branch", "enableAuth": "Enable Authentication", "authDescription": "Private repositories require authentication credentials", "username": "Username", "usernamePlaceholder": "Git username", "password": "Password/Access Token", "passwordPlaceholder": "Password or personal access token", "fileUploadDescription": "Upload files in ZIP, TAR, or TAR.GZ format", "uploadMethod": "Upload Method", "urlUpload": "URL Upload", "localFile": "Local File", "fileUrl": "File URL", "selectFile": "Select File", "selectedFile": "Selected File", "aiSettings": "AI Settings", "optional": "Optional", "model": "Model", "apiKey": "API Key", "endpoint": "API Endpoint", "customPrompt": "Custom Prompt", "promptPlaceholder": "Enter custom prompt (optional)", "submitSuccess": "Repository submitted successfully", "submitFailed": "Failed to submit repository", "submitError": "Submit failed", "branchPlaceholder": "Enter branch name or select from dropdown", "refreshBranches": "Refresh branch list", "branchHelperText": "You can select a branch from the dropdown or manually enter a branch name", "availableBranches": "Available branches", "fetchBranchesFailed": "Failed to fetch branch list", "fetchBranchesError": "Failed to fetch branches", "default": "default", "email": "Email", "emailPlaceholder": "Git email address", "validation": {"addressRequired": "Please enter repository address", "branchRequired": "Please select a branch", "fileRequired": "Please select a file to upload", "fileUrlRequired": "Please enter file URL", "organizationNameRequired": "Please enter organization name", "repositoryNameRequired": "Please enter repository name"}}, "nav": {"overview": "Overview", "documents": "Documents", "fileTree": "File Tree", "mindmap": "Knowledge Graph", "changelog": "Changelog", "settings": "Settings"}, "layout": {"branch": "Branch", "selectBranch": "Select Branch", "documentTree": "Document Tree", "noDocuments": "No documents available", "fetchBranchesError": "Failed to fetch branches", "fetchDocumentsError": "Failed to fetch documents", "loadingDocuments": "Loading documents...", "searchDocuments": "Search documents", "expandAll": "Expand All", "collapseAll": "Collapse All", "collapseSidebar": "Collapse Sidebar", "expandSidebar": "Expand Sidebar", "introduction": "Introduction", "repositoryNotFound": "Repository not found", "loadingRepository": "Loading repository...", "branchSwitcher": "Switch branch", "checkRepositoryAddress": "Please check if the repository address is correct", "backToHome": "Back to Home", "overview": "Overview", "mindMap": "Mind Map", "fileTree": "File Tree", "codeSearch": "Code Search", "mindMapDescription": "Visualize the knowledge structure and document relationships of the repository", "loadingMindMap": "Loading mind map...", "mindMapLoadFailed": "Failed to load mind map", "noMindMapData": "No mind map data available", "mindMapGenerating": "Mind map may be generating, please refresh later", "mindMapControls": "Controls", "searchNodes": "Search nodes", "totalNodes": "Total Nodes", "visibleNodes": "Visible", "zoomLevel": "Zoom Level", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "resetZoom": "Reset Zoom", "resetView": "Reset View", "exportSvg": "Export SVG", "nodeList": "Node List", "noSearchResults": "No search results", "showingFirst50": "Showing first 50 of {{total}} nodes", "fitToScreen": "Fit to Screen"}, "document": {"relevantSourceFiles": "Relevant source files", "showingSourceFiles": "Showing {{count}} source files", "tableOfContents": "Table of Contents", "loadFailed": "Failed to load document", "notFound": "Document not found"}, "detail": {"cloneCommand": "Clone Command", "statistics": "Statistics", "statisticsDescription": "Repository statistics overview", "documents": "Documents", "commits": "Commits", "contributors": "Contributors", "stars": "Stars", "loadFailed": "Failed to load repository information", "notFound": "Repository not found", "backToHome": "Back to Home", "export": "Export", "tabs": {"overview": "Overview", "documents": "Documents", "mindmap": "Knowledge Graph"}, "basicInfo": "Basic Information", "organization": "Organization", "repositoryName": "Repository Name", "status": "Status", "gitInfo": "Git Information", "repositoryUrl": "Repository URL", "branch": "Branch", "createdAt": "Created At", "updatedAt": "Updated At", "documentList": "Document List", "documentListDescription": "Browse and manage documents in the repository", "documentsComingSoon": "Documents feature coming soon...", "knowledgeGraph": "Knowledge Graph", "knowledgeGraphDescription": "Visualize the knowledge structure of the repository", "mindmapComingSoon": "Knowledge graph feature coming soon..."}}, "settings": {"title": "System Settings", "description": "Configure and manage system functionality settings", "subtitle": "Manage your account and preferences", "loadFailed": "Failed to load settings", "saveSuccess": "Setting<PERSON> saved successfully", "saveFailed": "Failed to save settings", "saveAll": "Save All Settings", "resetGroup": "Reset Group", "resetGroupConfirm": "Are you sure you want to reset all settings in current group to default values?", "resetSuccess": "Reset successful", "resetFailed": "Reset failed", "clearCache": "<PERSON>ache", "cacheCleared": "<PERSON><PERSON> cleared", "cacheClearFailed": "Failed to clear cache", "unsavedChanges": "Unsaved changes", "restartRequired": "<PERSON><PERSON> Required", "restartRequiredMessage": "Some setting changes require system restart to take effect", "restartInitiated": "System restart initiated", "restartFailed": "System restart failed", "importExport": {"title": "Import/Export", "export": "Export Settings", "import": "Import Settings", "exportNote": "Export Note", "exportNoteDescription": "Exported settings file does not include sensitive information like passwords and API keys", "importNote": "Import Warning", "importNoteDescription": "Importing settings will overwrite existing configuration. Please ensure you have a backup.", "exportSettings": "Export Settings", "selectGroups": "Select Groups", "selectGroupsDescription": "Choose groups to export; leave empty to export all", "selectGroupsPlaceholder": "Select setting groups", "downloadSettings": "Download Settings File", "exportSuccess": "Settings exported successfully", "exportFailed": "Failed to export settings", "uploadFile": "Upload File", "dragFileHere": "Click or drag file here to upload", "supportedFormats": "Supports JSON settings file", "importOptions": "Import Options", "selectImportGroups": "Select Import Groups", "selectImportGroupsDescription": "Choose groups to import", "overwriteExisting": "Overwrite Existing", "overwriteExistingDescription": "Whether to overwrite existing settings", "previewSettings": "Preview Settings ({{count}} items)", "key": "Key", "group": "Group", "description": "Description", "sensitive": "Sensitive", "fileLoaded": "File loaded successfully, {{count}} settings", "invalidFile": "Invalid settings file format", "selectFileFirst": "Please select a file first", "importSuccess": "Settings imported successfully", "importFailed": "Failed to import settings", "importInProgress": "Importing settings...", "importComplete": "Import complete", "importSettings": "Import Settings", "securityWarning": "Security Warning", "securityTip1": "Only import settings from trusted sources", "securityTip2": "Check settings content carefully before import", "securityTip3": "Sensitive information is not exported; reconfigure after import", "securityTip4": "Restart the system after import to ensure changes take effect", "showingFirst20": "Showing first 20 of {{total}} items"}, "validationFailed": "Configuration validation failed", "noSettingsAvailable": "No settings available", "groups": {"basic": "Basic Settings", "email": "Email Configuration", "ai": "AI Settings", "storage": "Storage Settings", "security": "Security Settings", "github": "GitHub Integration", "gitee": "Gitee Integration", "document": "Document Configuration", "jwt": "JWT Configuration", "backup": "Backup & Recovery", "logging": "Logging Settings"}, "navigation": {"profile": "Account Info", "security": "Security Settings", "preferences": "Preferences"}, "profile": {"title": "Account Information", "subtitle": "Manage your personal profile and basic information", "basic_info": "Basic Information", "basic_info_desc": "Manage your profile information", "avatar": {"upload": "Upload Avatar", "remove": "Remove Avatar", "upload_success": "Avatar uploaded successfully", "upload_failed": "Failed to upload avatar", "remove_success": "Avatar removed successfully", "remove_failed": "Failed to remove avatar", "confirm_remove": "Confirm Remove Avatar", "confirm_remove_desc": "Are you sure you want to remove the current avatar? This action cannot be undone.", "format_tip": "Supports JPG, PNG, GIF formats, file size no more than 2MB", "format_error": "Only JPG/PNG/GIF format images can be uploaded!", "size_error": "Image size cannot exceed 2MB!", "verified": "Verified"}, "form": {"username": "Username", "username_placeholder": "Enter username", "email": "Email Address", "email_placeholder": "Enter email address", "save_changes": "Save Changes", "saving": "Saving...", "update_success": "Personal information updated successfully", "update_failed": "Update failed, please try again"}, "validation": {"username_min": "Username must be at least 2 characters", "email_invalid": "Please enter a valid email address"}}, "security": {"title": "Security Settings", "subtitle": "Manage your account security and password settings", "change_password": "Change Password", "change_password_desc": "Regularly changing your password helps protect your account security. Please use a strong password.", "form": {"current_password": "Current Password", "current_password_placeholder": "Enter current password", "new_password": "New Password", "new_password_placeholder": "Enter new password", "confirm_password": "Confirm New Password", "confirm_password_placeholder": "Enter new password again", "password_requirements": "Password must be at least 8 characters and contain uppercase and lowercase letters and numbers", "update_password": "Update Password", "updating": "Updating...", "current_password_error": "Current password is incorrect", "update_success": "Password changed successfully", "update_failed": "Failed to change password, please try again"}, "validation": {"current_password_required": "Please enter current password", "new_password_min": "Password must be at least 8 characters", "new_password_pattern": "Password must contain uppercase and lowercase letters and numbers", "passwords_not_match": "Passwords do not match"}}, "preferences": {"title": "Preferences", "subtitle": "Customize your app experience and notification preferences", "app_settings": "App Settings", "app_settings_desc": "Manage your app preferences and notification options", "notifications": {"email_notifications": "Email Notifications", "email_notifications_desc": "Receive important updates and notification emails", "desktop_notifications": "Desktop Notifications", "desktop_notifications_desc": "Show real-time notifications on desktop"}, "general": {"auto_save": "Auto Save", "auto_save_desc": "Automatically save your work progress", "language": "Interface Language", "language_desc": "Choose your preferred interface language", "language_placeholder": "Select language"}, "auto_save_note": "Settings will be saved automatically"}, "common": {"back": "Back", "updating": "Updating...", "get_user_info_failed": "Failed to get user information, please login again", "load_user_info_failed": "Failed to load user information"}, "document": {"basicSettings": "Basic Settings", "readMaxTokens": "Maximum Read Tokens", "readMaxTokensHelp": "Maximum tokens to read per AI request (1000-200000)", "defaultLanguage": "Default Language", "defaultLanguageHelp": "Default programming language for documents and code", "defaultLanguagePlaceholder": "e.g. javascript", "proxy": "Git Proxy", "proxyHelp": "HTTP/HTTPS proxy for Git operations, format: http://proxy-server:port", "proxyPlaceholder": "http://proxy.example.com:8080", "enableAgentTool": "Enable Agent Tool", "enableAgentToolHelp": "Enable the Agent tool plugin to enhance functionality. By default, it provides in-depth planning capabilities.", "catalogueFormat": "Catalogue Format", "catalogueFormatPlaceholder": "Select catalogue format", "formatCompact": "Compact", "formatDetailed": "Detailed", "formatTree": "Tree", "formatFlat": "Flat", "fileFiltering": "File Filtering", "codeAnalysis": "Code Analysis", "warehouseTasks": "Repository Tasks", "enableIncrementalUpdate": "Enable Incremental Update", "enableIncrementalUpdateHelp": "Only process changed files to improve efficiency", "enableSmartFilter": "Enable Smart Filter", "enableSmartFilterHelp": "AI intelligently identifies and filters irrelevant files", "smartFilterNote": "Smart filtering can automatically identify configuration files, dependency files and other files that don't need processing", "excludedFiles": "Excluded Files", "excludedFilesHelp": "These files will be skipped during processing", "excludedFilesDescription": "Supports filename and extension matching", "excludedFilesPlaceholder": "*.log", "excludedFolders": "Excluded Folders", "excludedFoldersHelp": "These folders will be skipped during processing", "excludedFoldersDescription": "Supports relative and absolute paths", "excludedFoldersPlaceholder": "node_modules", "enableCodeDependencyAnalysis": "Enable Code Dependency Analysis", "enableCodeDependencyAnalysisHelp": "Analyze dependency relationships between code files", "enableCodeCompression": "Enable Code Compression", "enableCodeCompressionHelp": "Compress code content to save storage space", "supportedLanguages": "Supported Programming Languages", "supportedLanguagesHelp": "Select programming languages to process", "supportedLanguagesPlaceholder": "Select programming language", "enableWarehouseFunctionPromptTask": "Enable Repository Function Prompt Task", "enableWarehouseFunctionPromptTaskHelp": "Generate functional descriptions and usage tips for repositories", "enableWarehouseDescriptionTask": "Enable Repository Description Task", "enableWarehouseDescriptionTaskHelp": "Automatically generate detailed descriptions for repositories", "enableFileCommit": "Enable File Commit", "enableFileCommitHelp": "Automatically commit file changes after processing", "enableWarehouseCommit": "Enable Repository Commit", "enableWarehouseCommitHelp": "Automatically commit repository changes after processing", "refineAndEnhanceQuality": "Refine and Enhance Quality", "refineAndEnhanceQualityHelp": "Use AI to optimize document quality and readability", "processingStats": "Processing Statistics", "totalRepositories": "Total Repositories", "processedFiles": "Processed Files", "avgProcessingTime": "Average Processing Time", "successRate": "Success Rate", "optimizationTips": "Processing Optimization Tips", "tip1": "Enable smart filtering can significantly improve processing speed", "tip2": "Set reasonable exclusion rules for files and folders", "tip3": "Adjust maximum read tokens based on repository size and AI model limits", "tip4": "Regularly clean up failed processing tasks", "tip5": "Monitor AI model usage quotas"}}, "messages": {"network_error": "Network error, please check your connection", "server_error": "Server error, please try again later", "unknown_error": "Unknown error", "operation_success": "Operation successful", "operation_failed": "Operation failed"}, "admin": {"title": "<PERSON><PERSON>", "subtitle": "Manage users, roles, repositories and system settings", "nav": {"dashboard": "Dashboard", "users": "User Management", "roles": "Role Management", "repositories": "Repository Management", "settings": "System Settings"}, "dashboard": {"title": "Dashboard", "overview": "System Overview", "stats": {"total_users": "Total Users", "total_repos": "Total Repositories", "total_roles": "Total Roles", "active_sessions": "Active Sessions"}, "charts": {"user_growth": "User Growth", "repo_activity": "Repository Activity", "role_distribution": "Role Distribution"}}, "users": {"title": "User Management", "subtitle": "Manage system users and their information", "create": "Create User", "edit": "Edit User", "delete": "Delete User", "assign_roles": "Assign Roles", "search_placeholder": "Search by username or email", "table": {"username": "Username", "email": "Email", "role": "Role", "status": "Status", "created_at": "Created", "last_login": "Last Login", "actions": "Actions"}, "form": {"username": "Username", "email": "Email", "password": "Password", "confirm_password": "Confirm Password", "role": "Role", "avatar": "Avatar"}, "status": {"active": "Active", "inactive": "Inactive", "suspended": "Suspended"}}, "roles": {"title": "Role Management", "subtitle": "Manage roles and permissions", "create": "Create Role", "edit": "Edit Role", "delete": "Delete Role", "permissions": "Permissions", "search_placeholder": "Search by role name", "table": {"name": "Role Name", "description": "Description", "users_count": "Users", "permissions_count": "Permissions", "status": "Status", "created_at": "Created", "actions": "Actions"}, "form": {"name": "Role Name", "description": "Description", "status": "Status"}}, "repositories": {"title": "Repository Management", "subtitle": "Manage repositories and content", "search_placeholder": "Search by repository name", "table": {"name": "Repository Name", "organization": "Organization", "status": "Status", "documents": "Documents", "created_at": "Created", "updated_at": "Updated", "actions": "Actions"}, "detail": {"title": "Repository Details", "tree": "Content Tree", "editor": "Content Editor", "properties": "Properties", "permissions": "Permissions"}}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "back": "Back", "confirm": "Confirm", "yes": "Yes", "no": "No"}, "messages": {"create_success": "Created successfully", "update_success": "Updated successfully", "delete_success": "Deleted successfully", "delete_confirm": "Are you sure you want to delete this item?", "operation_failed": "Operation failed", "no_permission": "You don't have permission to access this page", "loading": "Loading...", "no_data": "No data available"}}}