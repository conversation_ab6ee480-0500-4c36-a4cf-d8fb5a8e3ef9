/**
 * 响应数据适配器
 * 用于转换Python API响应格式到前端期望的格式
 */

/**
 * Python API标准响应格式
 */
export interface PythonApiResponse<T = any> {
  data?: T
  message?: string
  success?: boolean
  error?: string
  detail?: string
}

/**
 * 前端期望的响应格式（.NET风格）
 */
export interface DotNetApiResponse<T = any> {
  code: number
  data?: T
  message?: string
  success?: boolean
  errorMessage?: string
}

/**
 * 转换Python API响应为前端期望的格式
 */
export function adaptPythonResponse<T>(
  response: any,
  status: number = 200
): DotNetApiResponse<T> {
  // 如果已经是期望格式，直接返回
  if (response && typeof response.code === 'number') {
    return response
  }

  // 处理错误响应
  if (status >= 400) {
    return {
      code: status,
      success: false,
      errorMessage: response?.detail || response?.error || response?.message || 'Request failed',
      data: undefined
    }
  }

  // 处理成功响应
  const success = response?.success !== false && status < 400

  return {
    code: status,
    success,
    data: response?.data || response,
    message: response?.message,
    errorMessage: success ? undefined : (response?.error || response?.detail)
  }
}

/**
 * 转换认证响应
 */
export function adaptAuthResponse(response: any, status: number = 200): DotNetApiResponse<any> {
  if (status >= 400) {
    return adaptPythonResponse(response, status)
  }

  // Python API直接返回用户信息和token
  if (response?.access_token || response?.token) {
    return {
      code: 200,
      success: true,
      data: {
        success: true,
        token: response.access_token || response.token,
        refreshToken: response.refresh_token || response.refreshToken,
        user: {
          id: response.user?.id || response.id,
          username: response.user?.username || response.username,
          email: response.user?.email || response.email,
          roleName: response.user?.role || response.role || 'user',
          avatar: response.user?.avatar || response.avatar
        }
      }
    }
  }

  return adaptPythonResponse(response, status)
}

/**
 * 转换分页响应
 */
export function adaptPageResponse<T>(response: any, status: number = 200): DotNetApiResponse<any> {
  if (status >= 400) {
    return adaptPythonResponse(response, status)
  }

  // Python API分页格式 vs .NET API格式
  if (response?.items || Array.isArray(response)) {
    const items = response?.items || response
    const total = response?.total || items.length
    const page = response?.page || 1
    const pageSize = response?.page_size || response?.pageSize || items.length

    return {
      code: 200,
      success: true,
      data: {
        items,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    }
  }

  return adaptPythonResponse(response, status)
}

/**
 * 转换仓库/仓库响应
 */
export function adaptWarehouseResponse(response: any, status: number = 200): DotNetApiResponse<any> {
  if (status >= 400) {
    return adaptPythonResponse(response, status)
  }

  // 字段名映射：Python API (snake_case) -> 前端期望 (camelCase/PascalCase)
  if (response?.id || response?.name) {
    const adaptedData = {
      id: response.id,
      name: response.name,
      description: response.description,
      gitUrl: response.git_url || response.gitUrl,
      branch: response.branch,
      status: response.status,
      createdAt: response.created_at || response.createdAt,
      updatedAt: response.updated_at || response.updatedAt,
      ownerId: response.owner_id || response.ownerId,
      // 添加其他可能的字段映射
      commitHash: response.commit_hash || response.commitHash,
      lastSyncAt: response.last_sync_at || response.lastSyncAt,
    }

    return {
      code: 200,
      success: true,
      data: adaptedData
    }
  }

  return adaptPythonResponse(response, status)
}

/**
 * 转换文档响应
 */
export function adaptDocumentResponse(response: any, status: number = 200): DotNetApiResponse<any> {
  if (status >= 400) {
    return adaptPythonResponse(response, status)
  }

  if (response?.id || response?.title) {
    const adaptedData = {
      id: response.id,
      title: response.title,
      description: response.description,
      warehouseId: response.warehouse_id || response.warehouseId,
      metaData: response.meta_data || response.metadata || response.metaData,
      extra: response.extra,
      createdAt: response.created_at || response.createdAt,
      updatedAt: response.updated_at || response.updatedAt,
    }

    return {
      code: 200,
      success: true,
      data: adaptedData
    }
  }

  return adaptPythonResponse(response, status)
}

/**
 * 根据路径选择合适的适配器
 */
export function adaptResponseByPath(path: string, response: any, status: number = 200): DotNetApiResponse<any> {
  const pathLower = path.toLowerCase()

  if (pathLower.includes('/auth/')) {
    return adaptAuthResponse(response, status)
  }
  
  if (pathLower.includes('/warehouse')) {
    return adaptWarehouseResponse(response, status)
  }
  
  if (pathLower.includes('/document')) {
    return adaptDocumentResponse(response, status)
  }
  
  if (pathLower.includes('/list') || pathLower.includes('page')) {
    return adaptPageResponse(response, status)
  }

  return adaptPythonResponse(response, status)
}
